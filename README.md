# <PERSON>'s Portfolio Website

A modern, animated portfolio website built with Next.js, TypeScript, Tailwind CSS, and ReactBits components featuring beautiful animations and a macOS-style dock navigation.

## 🚀 Getting Started

This project uses <PERSON><PERSON> as the package manager and includes various animated components from ReactBits.

### Prerequisites

- Node.js 18+
- Bun package manager

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   bun install
   ```

3. Run the development server:
   ```bash
   bun run dev
   ```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## 📦 ReactBits Components Installation

The following ReactBits components are used in this project. Install them using these commands:

### Text Animations

```bash
# Split Text Animation (for name "<PERSON>")
bunx jsrepo add https://reactbits.dev/ts/tailwind/TextAnimations/SplitText

# Scroll Velocity Animation (for background text "make experiences you will never forget")
bunx jsrepo add https://reactbits.dev/ts/tailwind/TextAnimations/ScrollVelocity

# Blur Text Animation (for "Full Stack Developer & UI/UX Designer")
bunx jsrepo add https://reactbits.dev/ts/tailwind/TextAnimations/BlurText

# Circular Text Animation (for "scroll for more info about me")
bunx jsrepo add https://reactbits.dev/ts/tailwind/TextAnimations/CircularText
```

### Components

```bash
# Tilted Card Component (for profile card with flip functionality)
bunx jsrepo add https://reactbits.dev/ts/tailwind/Components/TiltedCard
```

## 🎨 Features

- **Enhanced Hero Section**: Clean, modern design with improved spacing and gradient background
- **Split Text Animation**: "Abdullah" name with large, bold character-by-character 3D rotation animation
- **Background Scroll Text**: Subtle scrolling "make experiences you will never forget" message
- **Interactive Profile Card**: 3D tilted card with flip animation and "you can click it" hover tooltip
- **Blur Text Effects**: "Full Stack Developer & UI/UX Designer" with smooth word-by-word reveal
- **Circular Text**: Rotating "scroll for more info about me" text on card back side with speed-up on hover
- **Flippable Card**: Click the profile card to reveal the circular text animation on the back
- **macOS-style Dock**: Professional bottom navigation with:
  - Smooth magnification effects (56px base, 80px magnified)
  - Dark tooltips with arrows positioned correctly
  - Backdrop blur effects and shadows
  - Spring animations with realistic physics
  - Proper hover states and transitions
- **Enhanced Typography**: Larger fonts (up to 9xl for name) with better hierarchy
- **Gradient Background**: Subtle gradient from gray-50 via white to gray-100
- **Responsive Design**: Optimized for mobile, tablet, and desktop with proper scaling
- **Light Theme**: Clean, professional appearance with improved contrast
- **GSAP & Framer Motion**: Advanced animations using industry-standard libraries

## 🛠️ Tech Stack

- **Framework**: Next.js 15
- **Styling**: Tailwind CSS
- **Animations**: ReactBits components
- **Package Manager**: Bun
- **Language**: TypeScript

## 📁 Project Structure

```
├── app/
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/
│   └── [ReactBits components]
├── public/
│   └── profile-placeholder.svg
└── README.md
```

## 🎯 Component Usage

### SplitText
- **Purpose**: Animates "Abdullah" name with letter-by-letter 3D rotation
- **Features**: Character-based splitting, 3D rotation effects, scroll-triggered animation
- **Props**: Custom delay, duration, rotation effects, and easing

### ScrollVelocity
- **Purpose**: Creates infinite scrolling background text "make experiences you will never forget"
- **Features**: Velocity-based scrolling, scroll-responsive speed changes
- **Props**: Adjustable velocity, text array, custom styling

### TiltedCard (within FlippableProfileCard)
- **Purpose**: 3D interactive profile card with mouse tracking
- **Features**: 3D tilt effects, hover scaling, tooltip on hover
- **Props**: Image source, hover text, tilt amplitude, scale effects

### BlurText
- **Purpose**: Animates subtitle "Full Stack Developer & UI/UX Designer"
- **Features**: Word-by-word blur-to-clear animation, intersection observer
- **Props**: Custom blur effects, animation timing, direction control

### CircularText
- **Purpose**: Rotating "scroll for more info about me" text on card back
- **Features**: Circular text layout, hover speed changes, continuous rotation
- **Props**: Spin duration, hover effects, custom styling

### FlippableProfileCard (Custom Component)
- **Purpose**: Combines TiltedCard with flip functionality
- **Features**: Click to flip, reveals CircularText on back, smooth 3D transitions
- **Integration**: Uses Framer Motion for flip animation, combines multiple ReactBits components

## 🚀 Deployment

Build the project for production:

```bash
bun run build
```

The project is optimized for deployment on Vercel, Netlify, or any static hosting platform.

## 📝 Notes

- All environment variables should be placed in `.env.local` (not `.env`)
- The project uses Bun instead of npm/yarn for faster package management
- ReactBits components are installed in the `components/` directory

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!
