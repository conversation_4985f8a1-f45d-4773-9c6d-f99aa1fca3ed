'use client';

import { useEffect, useState } from "react";
import SplitText from "../components/SplitText/SplitText";
import ScrollVelocity from "../components/ScrollVelocity/ScrollVelocity";
import BlurText from "../components/BlurText/BlurText";
import FlippableProfileCard from "../components/FlippableProfileCard";
import { AnimatedTooltip } from "../components/ui/animated-tooltip";
import Dock from "../components/Dock/Dock";

export default function Home() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  // Happy clients data for animated tooltip - using professional photos
  const happyClients = [
    {
      id: 1,
      name: "<PERSON>",
      designation: "CEO at TechCorp",
      image: "https://randomuser.me/api/portraits/women/44.jpg",
    },
    {
      id: 2,
      name: "<PERSON>",
      designation: "CTO at StartupXYZ",
      image: "https://randomuser.me/api/portraits/men/32.jpg",
    },
    {
      id: 3,
      name: "<PERSON>",
      designation: "Designer at Creative Co",
      image: "https://randomuser.me/api/portraits/women/68.jpg",
    },
    {
      id: 4,
      name: "David Kim",
      designation: "Founder at InnovateLab",
      image: "https://randomuser.me/api/portraits/men/46.jpg",
    },
  ];

  // Dock navigation items
  const dockItems = [
    {
      icon: (
        <svg className="w-7 h-7 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
        </svg>
      ),
      label: "Home",
      onClick: () => console.log("Home clicked"),
    },
    {
      icon: (
        <svg className="w-7 h-7 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      ),
      label: "About",
      onClick: () => console.log("About clicked"),
    },
    {
      icon: (
        <svg className="w-7 h-7 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ),
      label: "Projects",
      onClick: () => console.log("Projects clicked"),
    },
    {
      icon: (
        <svg className="w-7 h-7 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
      ),
      label: "Contact",
      onClick: () => console.log("Contact clicked"),
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 relative overflow-hidden">
      {/* Full Background Scrolling Text */}
      <div className="fixed inset-0 flex items-center justify-center pointer-events-none z-10">
        <ScrollVelocity
          texts={["make experiences you will never forget"]}
          velocity={30}
          className="text-gray-200"
          parallaxClassName="w-full"
          scrollerClassName="text-4xl md:text-7xl lg:text-8xl font-black whitespace-nowrap tracking-widest opacity-20"
        />
      </div>

      {/* Main hero content */}
      <div className="container mx-auto px-6 pt-16 pb-32 relative z-30">
        <div className="text-center max-w-5xl mx-auto">

          {/* Profile Card */}
          <div className="relative mb-10">
            <div className={`relative z-20 transition-all duration-1200 delay-300 ${isLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}>
              <FlippableProfileCard
                imageSrc="/profile-placeholder.svg"
                altText="Abdullah - Full Stack Developer"
              />
            </div>
          </div>

          {/* Name with Split Text Animation - UNDER the profile card */}
          <div className={`mb-8 transition-all duration-1000 delay-500 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}>
            <SplitText
              text="Abdullah"
              className="text-6xl md:text-8xl lg:text-9xl font-black text-black tracking-tight"
              delay={120}
              duration={0.9}
              splitType="chars"
              from={{ opacity: 0, y: 60, rotateX: -90, scale: 0.8 }}
              to={{ opacity: 1, y: 0, rotateX: 0, scale: 1 }}
            />
          </div>

          {/* Subtitle with Blur Text Animation - UNDER the name */}
          <div className={`mb-16 transition-all duration-1000 delay-700 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}>
            <div className="text-xl md:text-2xl lg:text-3xl text-gray-700 flex justify-center">
              <BlurText
                text="Full Stack Developer & UI/UX Designer"
                className="font-medium tracking-wide"
                delay={80}
                animateBy="words"
                direction="bottom"
              />
            </div>
          </div>

          {/* Clean Client Avatars */}
          <div className={`flex justify-center mb-16 transition-all duration-1000 delay-900 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="flex items-center">
              <AnimatedTooltip items={happyClients} />
            </div>
          </div>

          {/* CTA Button */}
          <div className={`transition-all duration-1000 delay-1100 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <button className="bg-gradient-to-r from-black to-gray-800 text-white px-12 py-5 rounded-full font-bold text-xl hover:from-gray-800 hover:to-black transition-all duration-300 hover:scale-105 shadow-2xl hover:shadow-3xl transform hover:-translate-y-1">
              Let's Work Together!
            </button>
          </div>
        </div>
      </div>

      {/* Bottom Dock Navigation */}
      <div className="fixed bottom-0 left-0 right-0 z-50">
        <Dock
          items={dockItems}
          magnification={80}
          distance={140}
          baseItemSize={56}
          panelHeight={72}
          spring={{ mass: 0.1, stiffness: 300, damping: 20 }}
        />
      </div>
    </div>
  );
}
