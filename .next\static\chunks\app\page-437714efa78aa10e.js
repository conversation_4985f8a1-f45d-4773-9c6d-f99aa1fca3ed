(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{4957:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>C});var a=l(5155),s=l(2115),r=l(802),n=l(9088),i=l(5228);r.os.registerPlugin(n.u,i.A);let o=e=>{let{text:t,className:l="",delay:n=100,duration:o=.6,ease:c="power3.out",splitType:d="chars",from:u={opacity:0,y:40},to:p={opacity:1,y:0},threshold:m=.1,rootMargin:x="-100px",textAlign:f="center",onLetterAnimationComplete:h}=e,y=(0,s.useRef)(null),g=(0,s.useRef)(!1),v=(0,s.useRef)(null);return(0,s.useEffect)(()=>{let e,l;if(!y.current||!t)return;let a=y.current;g.current=!1;let s="lines"===d;s&&(a.style.position="relative");try{e=new i.A(a,{type:d,absolute:s,linesClass:"split-line"})}catch(e){console.error("Failed to create SplitText:",e);return}switch(d){case"lines":l=e.lines;break;case"words":l=e.words;break;default:l=e.chars}if(!l||0===l.length){console.warn("No targets found for SplitText animation"),e.revert();return}l.forEach(e=>{e.style.willChange="transform, opacity"});let f=(1-m)*100,b=/^(-?\d+(?:\.\d+)?)(px|em|rem|%)?$/.exec(x),w=b?parseFloat(b[1]):0,j=b&&b[2]||"px",N=w<0?"-=".concat(Math.abs(w)).concat(j):"+=".concat(w).concat(j),k="top ".concat(f,"%").concat(N),C=r.os.timeline({scrollTrigger:{trigger:a,start:k,toggleActions:"play none none none",once:!0,onToggle:e=>{v.current=e}},smoothChildTiming:!0,onComplete:()=>{g.current=!0,r.os.set(l,{...p,clearProps:"willChange",immediateRender:!0}),null==h||h()}});return C.set(l,{...u,immediateRender:!1,force3D:!0}),C.to(l,{...p,duration:o,ease:c,stagger:n/1e3,force3D:!0}),()=>{C.kill(),v.current&&(v.current.kill(),v.current=null),r.os.killTweensOf(l),e&&e.revert()}},[t,n,o,c,d,u,p,m,x,h]),(0,a.jsx)("p",{ref:y,className:"split-parent overflow-hidden inline-block whitespace-normal ".concat(l),style:{textAlign:f,wordWrap:"break-word"},children:t})};var c=l(8619),d=l(2720),u=l(6836),p=l(7602),m=l(8829),x=l(4087),f=l(6295);let h=e=>{let{scrollContainerRef:t,texts:l=[],velocity:r=100,className:n="",damping:i=50,stiffness:o=400,numCopies:h=6,velocityMapping:y={input:[0,1e3],output:[0,5]},parallaxClassName:g,scrollerClassName:v,parallaxStyle:b,scrollerStyle:w}=e;function j(e){let{children:t,baseVelocity:l=r,scrollContainerRef:n,className:i="",damping:o,stiffness:h,numCopies:y,velocityMapping:g,parallaxClassName:v,scrollerClassName:b,parallaxStyle:w,scrollerStyle:j}=e,N=(0,c.d)(0),{scrollY:k}=(0,d.L)(n?{container:n}:{}),C=(0,u.V)(k),z=(0,p.z)(C,{damping:null!=o?o:50,stiffness:null!=h?h:400}),S=(0,m.G)(z,(null==g?void 0:g.input)||[0,1e3],(null==g?void 0:g.output)||[0,5],{clamp:!1}),T=(0,s.useRef)(null),E=function(e){let[t,l]=(0,s.useState)(0);return(0,s.useLayoutEffect)(()=>{function t(){e.current&&l(e.current.offsetWidth)}return t(),window.addEventListener("resize",t),()=>window.removeEventListener("resize",t)},[e]),t}(T),M=(0,m.G)(N,e=>0===E?"0px":"".concat(function(e,t,l){let a=0-e;return((l-e)%a+a)%a+e}(-E,0,e),"px")),P=(0,s.useRef)(1);(0,x.N)((e,t)=>{let a=P.current*l*(t/1e3);0>S.get()?P.current=-1:S.get()>0&&(P.current=1),a+=P.current*a*S.get(),N.set(N.get()+a)});let R=[];for(let e=0;e<y;e++)R.push((0,a.jsx)("span",{className:"flex-shrink-0 ".concat(i),ref:0===e?T:null,children:t},e));return(0,a.jsx)("div",{className:"".concat(v," relative overflow-hidden"),style:w,children:(0,a.jsx)(f.P.div,{className:"".concat(b," flex whitespace-nowrap text-center font-sans text-4xl font-bold tracking-[-0.02em] drop-shadow md:text-[5rem] md:leading-[5rem]"),style:{x:M,...j},children:R})})}return(0,a.jsx)("section",{children:l.map((e,l)=>(0,a.jsxs)(j,{className:n,baseVelocity:l%2!=0?-r:r,scrollContainerRef:t,damping:i,stiffness:o,numCopies:h,velocityMapping:y,parallaxClassName:g,scrollerClassName:v,parallaxStyle:b,scrollerStyle:w,children:[e,"\xa0"]},l))})},y=e=>{let{text:t="",delay:l=200,className:r="",animateBy:n="words",direction:i="top",threshold:o=.1,rootMargin:c="0px",animationFrom:d,animationTo:u,easing:p=e=>e,onAnimationComplete:m,stepDuration:x=.35}=e,h="words"===n?t.split(" "):t.split(""),[y,g]=(0,s.useState)(!1),v=(0,s.useRef)(null);(0,s.useEffect)(()=>{if(!v.current)return;let e=new IntersectionObserver(t=>{let[l]=t;l.isIntersecting&&(g(!0),e.unobserve(v.current))},{threshold:o,rootMargin:c});return e.observe(v.current),()=>e.disconnect()},[o,c]);let b=(0,s.useMemo)(()=>"top"===i?{filter:"blur(10px)",opacity:0,y:-50}:{filter:"blur(10px)",opacity:0,y:50},[i]),w=(0,s.useMemo)(()=>[{filter:"blur(5px)",opacity:.5,y:"top"===i?5:-5},{filter:"blur(0px)",opacity:1,y:0}],[i]),j=null!=d?d:b,N=null!=u?u:w,k=N.length+1,C=x*(k-1),z=Array.from({length:k},(e,t)=>1===k?0:t/(k-1));return(0,a.jsx)("p",{ref:v,className:"blur-text ".concat(r," flex flex-wrap"),children:h.map((e,t)=>{let s=((e,t)=>{let l=new Set([...Object.keys(e),...t.flatMap(e=>Object.keys(e))]),a={};return l.forEach(l=>{a[l]=[e[l],...t.map(e=>e[l])]}),a})(j,N),r={duration:C,times:z,delay:t*l/1e3};return r.ease=p,(0,a.jsxs)(f.P.span,{initial:j,animate:y?s:j,transition:r,onAnimationComplete:t===h.length-1?m:void 0,style:{display:"inline-block",willChange:"transform, filter, opacity"},children:[" "===e?"\xa0":e,"words"===n&&t<h.length-1&&"\xa0"]},t)})})},g={damping:30,stiffness:100,mass:2};function v(e){let{imageSrc:t,altText:l="Tilted card image",captionText:r="",containerHeight:n="300px",containerWidth:i="100%",imageHeight:o="300px",imageWidth:d="300px",scaleOnHover:u=1.1,rotateAmplitude:m=14,showMobileWarning:x=!0,showTooltip:h=!0,overlayContent:y=null,displayOverlayContent:v=!1}=e,b=(0,s.useRef)(null),w=(0,c.d)(0),j=(0,c.d)(0),N=(0,p.z)((0,c.d)(0),g),k=(0,p.z)((0,c.d)(0),g),C=(0,p.z)(1,g),z=(0,p.z)(0),S=(0,p.z)(0,{stiffness:350,damping:30,mass:1}),[T,E]=(0,s.useState)(0);return(0,a.jsxs)("figure",{ref:b,className:"relative w-full h-full [perspective:800px] flex flex-col items-center justify-center",style:{height:n,width:i},onMouseMove:function(e){if(!b.current)return;let t=b.current.getBoundingClientRect(),l=e.clientX-t.left-t.width/2,a=e.clientY-t.top-t.height/2,s=-(a/(t.height/2)*m),r=l/(t.width/2)*m;N.set(s),k.set(r),w.set(e.clientX-t.left),j.set(e.clientY-t.top);let n=a-T;S.set(-(.6*n)),E(a)},onMouseEnter:function(){C.set(u),z.set(1)},onMouseLeave:function(){z.set(0),C.set(1),N.set(0),k.set(0),S.set(0)},children:[x&&(0,a.jsx)("div",{className:"absolute top-4 text-center text-sm block sm:hidden",children:"This effect is not optimized for mobile. Check on desktop."}),(0,a.jsxs)(f.P.div,{className:"relative [transform-style:preserve-3d]",style:{width:d,height:o,rotateX:N,rotateY:k,scale:C},children:[(0,a.jsx)(f.P.img,{src:t,alt:l,className:"absolute top-0 left-0 object-cover rounded-[15px] will-change-transform [transform:translateZ(0)]",style:{width:d,height:o}}),v&&y&&(0,a.jsx)(f.P.div,{className:"absolute top-0 left-0 z-[2] will-change-transform [transform:translateZ(30px)]",children:y})]}),h&&(0,a.jsx)(f.P.figcaption,{className:"pointer-events-none absolute left-0 top-0 rounded-[4px] bg-white px-[10px] py-[4px] text-[10px] text-[#2d2d2d] opacity-0 z-[3] hidden sm:block",style:{x:w,y:j,opacity:z,rotate:S},children:r})]})}var b=l(6896);let w=function(e,t){let l=!(arguments.length>2)||void 0===arguments[2]||arguments[2];return{from:t,to:t+360,ease:"linear",duration:e,type:"tween",repeat:l?1/0:0}},j=(e,t)=>({rotate:w(e,t),scale:{type:"spring",damping:20,stiffness:300}}),N=e=>{let{text:t,spinDuration:l=20,onHover:r="speedUp",className:n=""}=e,i=Array.from(t),o=(0,b.s)(),d=(0,c.d)(0);return(0,s.useEffect)(()=>{let e=d.get();o.start({rotate:e+360,scale:1,transition:j(l,e)})},[l,t,r,o]),(0,a.jsx)(f.P.div,{className:"m-0 mx-auto rounded-full w-[200px] h-[200px] relative font-black text-center cursor-pointer origin-center ".concat(n),style:{rotate:d},initial:{rotate:0},animate:o,onMouseEnter:()=>{let e,t=d.get();if(!r)return;let a=1;switch(r){case"slowDown":e=j(2*l,t);break;case"speedUp":e=j(l/4,t);break;case"pause":e={rotate:{type:"spring",damping:20,stiffness:300},scale:{type:"spring",damping:20,stiffness:300}};break;case"goBonkers":e=j(l/20,t),a=.8;break;default:e=j(l,t)}o.start({rotate:t+360,scale:a,transition:e})},onMouseLeave:()=>{let e=d.get();o.start({rotate:e+360,scale:1,transition:j(l,e)})},children:i.map((e,t)=>{let l=360/i.length*t,s=Math.PI/i.length,r="rotateZ(".concat(l,"deg) translate3d(").concat(s*t,"px, ").concat(s*t,"px, 0)");return(0,a.jsx)("span",{className:"absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]",style:{transform:r,WebkitTransform:r},children:e},t)})})};function k(e){let{imageSrc:t,altText:l="Abdullah - Full Stack Developer"}=e,[r,n]=(0,s.useState)(!1);return(0,a.jsx)("div",{className:"relative w-64 h-64 md:w-80 md:h-80 mx-auto [perspective:1000px]",children:(0,a.jsxs)(f.P.div,{className:"relative w-full h-full [transform-style:preserve-3d] cursor-pointer",animate:{rotateY:180*!!r},transition:{duration:.8,ease:"easeInOut"},onClick:()=>{n(!r)},children:[(0,a.jsx)("div",{className:"absolute inset-0 w-full h-full [backface-visibility:hidden]",children:(0,a.jsx)(v,{imageSrc:t,altText:l,captionText:"you can click it",containerHeight:"100%",containerWidth:"100%",imageHeight:"100%",imageWidth:"100%",scaleOnHover:1.05,rotateAmplitude:12,showMobileWarning:!1,showTooltip:!0})}),(0,a.jsx)("div",{className:"absolute inset-0 w-full h-full [backface-visibility:hidden] [transform:rotateY(180deg)]",children:(0,a.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-slate-50 to-gray-100 border-2 border-gray-200 rounded-3xl flex items-center justify-center shadow-2xl",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(N,{text:"scroll for more info about me • ",spinDuration:15,onHover:"speedUp",className:"text-gray-700 font-medium"}),(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsx)("div",{className:"w-16 h-16 bg-gray-200/80 rounded-full flex items-center justify-center border border-gray-300",children:(0,a.jsx)("svg",{className:"w-8 h-8 text-gray-600 animate-bounce",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 14l-7 7m0 0l-7-7m7 7V3"})})})})]})})})]})})}function C(){let[e,t]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{t(!0)},[]),(0,a.jsx)("div",{className:"min-h-screen bg-white relative overflow-hidden",children:(0,a.jsx)("div",{className:"container mx-auto px-6 pt-20 pb-20 relative z-10",children:(0,a.jsxs)("div",{className:"text-center max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"relative mb-8",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center pointer-events-none opacity-5 z-0 overflow-hidden",children:(0,a.jsx)("div",{className:"w-full",children:(0,a.jsx)(h,{texts:["make experiences you will never forget"],velocity:25,className:"text-gray-400",parallaxClassName:"w-full",scrollerClassName:"text-3xl md:text-5xl font-bold whitespace-nowrap tracking-wider"})})}),(0,a.jsx)("div",{className:"relative z-20 transition-all duration-1000 delay-300 ".concat(e?"opacity-100 scale-100":"opacity-0 scale-95"),children:(0,a.jsx)(k,{imageSrc:"/profile-placeholder.svg",altText:"Abdullah - Full Stack Developer"})})]}),(0,a.jsx)("div",{className:"mb-6 transition-all duration-1000 delay-500 ".concat(e?"opacity-100":"opacity-0"),children:(0,a.jsx)(o,{text:"Abdullah",className:"text-5xl md:text-7xl font-bold text-black",delay:150,duration:.8,splitType:"chars",from:{opacity:0,y:50,rotateX:-90},to:{opacity:1,y:0,rotateX:0}})}),(0,a.jsx)("div",{className:"mb-12 transition-all duration-1000 delay-700 ".concat(e?"opacity-100":"opacity-0"),children:(0,a.jsx)("div",{className:"text-lg md:text-xl text-gray-600 whitespace-nowrap",children:(0,a.jsx)(y,{text:"Full Stack Developer & UI/UX Designer",className:"inline-block",delay:100,animateBy:"words",direction:"bottom"})})}),(0,a.jsx)("div",{className:"flex justify-center gap-4 mb-12 transition-all duration-1000 delay-900 ".concat(e?"opacity-100 translate-y-0":"opacity-0 translate-y-8"),children:(0,a.jsxs)("div",{className:"flex items-center gap-3 px-6 py-3 bg-gray-50 rounded-full border border-gray-200 shadow-sm",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("span",{className:"text-yellow-500 animate-pulse",children:"⭐"}),(0,a.jsx)("span",{className:"text-yellow-500 animate-pulse delay-150",children:"⭐"}),(0,a.jsx)("span",{className:"text-yellow-500 animate-pulse delay-300",children:"⭐"}),(0,a.jsx)("span",{className:"text-yellow-500 animate-pulse delay-450",children:"⭐"}),(0,a.jsx)("span",{className:"text-yellow-500 animate-pulse delay-600",children:"⭐"})]}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"80+ Happy Clients"})]})}),(0,a.jsx)("div",{className:"transition-all duration-1000 delay-1100 ".concat(e?"opacity-100 translate-y-0":"opacity-0 translate-y-8"),children:(0,a.jsx)("button",{className:"bg-gray-900 text-white px-10 py-4 rounded-full font-semibold text-lg hover:bg-gray-800 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl border border-gray-800",children:"Let's Work Together!"})})]})})})}},6983:(e,t,l)=>{Promise.resolve().then(l.bind(l,4957))}},e=>{e.O(0,[592,450,441,964,358],()=>e(e.s=6983)),_N_E=e.O()}]);