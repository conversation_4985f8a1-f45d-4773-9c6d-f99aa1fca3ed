'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import TiltedCard from './TiltedCard/TiltedCard';
import CircularText from './CircularText/CircularText';

interface FlippableProfileCardProps {
  imageSrc: string;
  altText?: string;
}

export default function FlippableProfileCard({ 
  imageSrc, 
  altText = "Abdullah - Full Stack Developer" 
}: FlippableProfileCardProps) {
  const [isFlipped, setIsFlipped] = useState(false);

  const handleCardClick = () => {
    setIsFlipped(!isFlipped);
  };

  return (
    <div className="relative w-64 h-64 md:w-80 md:h-80 mx-auto [perspective:1000px]">
      <motion.div
        className="relative w-full h-full [transform-style:preserve-3d] cursor-pointer"
        animate={{ rotateY: isFlipped ? 180 : 0 }}
        transition={{ duration: 0.8, ease: "easeInOut" }}
        onClick={handleCardClick}
      >
        {/* Front Side */}
        <div className="absolute inset-0 w-full h-full [backface-visibility:hidden]">
          <TiltedCard
            imageSrc={imageSrc}
            altText={altText}
            captionText="you can click it"
            containerHeight="100%"
            containerWidth="100%"
            imageHeight="100%"
            imageWidth="100%"
            scaleOnHover={1.05}
            rotateAmplitude={12}
            showMobileWarning={false}
            showTooltip={true}
          />
        </div>

        {/* Back Side */}
        <div className="absolute inset-0 w-full h-full [backface-visibility:hidden] [transform:rotateY(180deg)]">
          <div className="w-full h-full bg-white border border-gray-200 rounded-3xl flex items-center justify-center shadow-2xl">
            <div className="relative">
              <CircularText
                text="scroll down for finding more about me • "
                spinDuration={20}
                onHover="speedUp"
                className="text-gray-600 font-medium text-sm"
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                  <svg
                    className="w-5 h-5 text-gray-500 animate-bounce"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 14l-7 7m0 0l-7-7m7 7V3"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
