/* [project]/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: rgba(0, 0, 0, 0);
      --tw-gradient-via: rgba(0, 0, 0, 0);
      --tw-gradient-to: rgba(0, 0, 0, 0);
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-leading: initial;
      --tw-animation-delay: 0s;
      --tw-animation-direction: normal;
      --tw-animation-duration: initial;
      --tw-animation-fill-mode: none;
      --tw-animation-iteration-count: 1;
      --tw-enter-opacity: 1;
      --tw-enter-rotate: 0;
      --tw-enter-scale: 1;
      --tw-enter-translate-x: 0;
      --tw-enter-translate-y: 0;
      --tw-exit-opacity: 1;
      --tw-exit-rotate: 0;
      --tw-exit-scale: 1;
      --tw-exit-translate-x: 0;
      --tw-exit-translate-y: 0;
    }

    ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: rgba(0, 0, 0, 0);
      --tw-gradient-via: rgba(0, 0, 0, 0);
      --tw-gradient-to: rgba(0, 0, 0, 0);
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-leading: initial;
      --tw-animation-delay: 0s;
      --tw-animation-direction: normal;
      --tw-animation-duration: initial;
      --tw-animation-fill-mode: none;
      --tw-animation-iteration-count: 1;
      --tw-enter-opacity: 1;
      --tw-enter-rotate: 0;
      --tw-enter-scale: 1;
      --tw-enter-translate-x: 0;
      --tw-enter-translate-y: 0;
      --tw-exit-opacity: 1;
      --tw-exit-rotate: 0;
      --tw-exit-scale: 1;
      --tw-exit-translate-x: 0;
      --tw-exit-translate-y: 0;
    }
  }
}

@layer theme {
  :root, :host {
    --color-yellow-500: #edb200;
    --color-emerald-500: #00bb7f;
    --color-sky-500: #00a5ef;
    --color-slate-50: #f8fafc;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5dc;
    --color-gray-400: #99a1af;
    --color-gray-500: #6a7282;
    --color-gray-600: #4a5565;
    --color-gray-700: #364153;
    --color-gray-800: #1e2939;
    --color-gray-900: #101828;
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-4xl: 56rem;
    --container-5xl: 64rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --text-7xl: 4.5rem;
    --text-7xl--line-height: 1;
    --text-8xl: 6rem;
    --text-8xl--line-height: 1;
    --text-9xl: 8rem;
    --text-9xl--line-height: 1;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-black: 900;
    --tracking-tight: -.025em;
    --tracking-wide: .025em;
    --tracking-wider: .05em;
    --tracking-widest: .1em;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --animate-bounce: bounce 1s infinite;
    --blur-sm: 8px;
    --blur-md: 12px;
    --blur-xl: 24px;
    --blur-2xl: 40px;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
  }

  @supports (color: color(display-p3 0 0 0)) {
    :root, :host {
      --color-yellow-500: color(display-p3 .903651 .703062 .0745389);
      --color-emerald-500: color(display-p3 .267113 .726847 .508397);
      --color-sky-500: color(display-p3 .219113 .639027 .931479);
      --color-slate-50: color(display-p3 .974377 .979815 .986207);
      --color-gray-100: color(display-p3 .953567 .956796 .964321);
      --color-gray-200: color(display-p3 .899787 .906171 .92106);
      --color-gray-300: color(display-p3 .822033 .835264 .858521);
      --color-gray-400: color(display-p3 .605734 .630385 .680158);
      --color-gray-500: color(display-p3 .421287 .446085 .504784);
      --color-gray-600: color(display-p3 .297358 .332176 .39043);
      --color-gray-700: color(display-p3 .219968 .253721 .318679);
      --color-gray-800: color(display-p3 .125854 .159497 .216835);
      --color-gray-900: color(display-p3 .070423 .0928982 .151928);
    }
  }

  @supports (color: lab(0% 0 0)) {
    :root, :host {
      --color-yellow-500: lab(76.3898% 14.5258 98.4589);
      --color-emerald-500: lab(66.9756% -58.27 19.5419);
      --color-sky-500: lab(63.3038% -18.433 -51.0407);
      --color-slate-50: lab(98.1434% -.369549 -1.05968);
      --color-gray-100: lab(96.1596% -.082314 -1.13575);
      --color-gray-200: lab(91.6229% -.159085 -2.26791);
      --color-gray-300: lab(85.1236% -.612259 -3.7138);
      --color-gray-400: lab(65.9269% -.832707 -8.17474);
      --color-gray-500: lab(47.7841% -.393212 -10.0268);
      --color-gray-600: lab(35.6337% -1.58697 -10.8425);
      --color-gray-700: lab(27.1134% -.956401 -12.3224);
      --color-gray-800: lab(16.1051% -1.18239 -11.7533);
      --color-gray-900: lab(8.11897% .811279 -12.254);
    }
  }
}

@layer base {
  *, :after, :before {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::backdrop {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::-webkit-file-upload-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  ::-webkit-file-upload-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:-moz-any([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup option:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:-moz-any([multiple], [size])) optgroup option:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:is([multiple], [size])) optgroup option:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup option:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :where(select:-moz-any([multiple], [size])) optgroup option:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :where(select:is([multiple], [size])) optgroup option:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-file-upload-button {
    margin-right: 4px;
  }

  :not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::file-selector-button {
    margin-right: 4px;
  }

  :not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::file-selector-button {
    margin-right: 4px;
  }

  :-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-file-upload-button {
    margin-left: 4px;
  }

  :-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::file-selector-button {
    margin-left: 4px;
  }

  :is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::file-selector-button {
    margin-left: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-year-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-month-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-day-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-hour-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-minute-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-second-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-millisecond-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-meridiem-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  input:where([type="button"], [type="reset"], [type="submit"]) {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::-webkit-file-upload-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::file-selector-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::-webkit-inner-spin-button {
    height: auto;
  }

  ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }

  * {
    border-color: var(--border);
    outline-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    * {
      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  body {
    background-color: var(--background);
    color: var(--foreground);
  }
}

@layer components;

@layer utilities {
  .pointer-events-none {
    pointer-events: none;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .static {
    position: static;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .inset-x-10 {
    inset-inline: calc(var(--spacing) * 10);
  }

  .-top-6 {
    top: calc(var(--spacing) * -6);
  }

  .-top-12 {
    top: calc(var(--spacing) * -12);
  }

  .-top-16 {
    top: calc(var(--spacing) * -16);
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-4 {
    top: calc(var(--spacing) * 4);
  }

  .top-full {
    top: 100%;
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .-bottom-px {
    bottom: -1px;
  }

  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }

  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }

  .bottom-6 {
    bottom: calc(var(--spacing) * 6);
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .left-1\/2 {
    left: 50%;
  }

  .left-10 {
    left: calc(var(--spacing) * 10);
  }

  .z-0 {
    z-index: 0;
  }

  .z-10 {
    z-index: 10;
  }

  .z-20 {
    z-index: 20;
  }

  .z-30 {
    z-index: 30;
  }

  .z-50 {
    z-index: 50;
  }

  .z-\[2\] {
    z-index: 2;
  }

  .z-\[3\] {
    z-index: 3;
  }

  .container {
    width: 100%;
  }

  @media (min-width: 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (min-width: 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (min-width: 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (min-width: 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (min-width: 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .\!m-0 {
    margin: calc(var(--spacing) * 0) !important;
  }

  .m-0 {
    margin: calc(var(--spacing) * 0);
  }

  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }

  .mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .-mr-4 {
    margin-right: calc(var(--spacing) * -4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }

  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }

  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }

  .block {
    display: block;
  }

  .flex {
    display: flex;
  }

  .hidden {
    display: none;
  }

  .inline-block {
    display: inline-block;
  }

  .inline-flex {
    display: inline-flex;
  }

  .h-0 {
    height: calc(var(--spacing) * 0);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-7 {
    height: calc(var(--spacing) * 7);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-14 {
    height: calc(var(--spacing) * 14);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-64 {
    height: calc(var(--spacing) * 64);
  }

  .h-\[200px\] {
    height: 200px;
  }

  .h-full {
    height: 100%;
  }

  .h-px {
    height: 1px;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-0 {
    width: calc(var(--spacing) * 0);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-7 {
    width: calc(var(--spacing) * 7);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-14 {
    width: calc(var(--spacing) * 14);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-64 {
    width: calc(var(--spacing) * 64);
  }

  .w-\[20\%\] {
    width: 20%;
  }

  .w-\[40\%\] {
    width: 40%;
  }

  .w-\[200px\] {
    width: 200px;
  }

  .w-fit {
    width: -moz-fit-content;
    width: fit-content;
  }

  .w-full {
    width: 100%;
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-5xl {
    max-width: var(--container-5xl);
  }

  .max-w-full {
    max-width: 100%;
  }

  .flex-shrink-0 {
    flex-shrink: 0;
  }

  .origin-center {
    transform-origin: center;
  }

  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-0 {
    --tw-translate-y: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-8 {
    --tw-translate-y: calc(var(--spacing) * 8);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .scale-95 {
    --tw-scale-x: 95%;
    --tw-scale-y: 95%;
    --tw-scale-z: 95%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-100 {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .\[transform\:rotateY\(180deg\)\] {
    transform: rotateY(180deg);
  }

  .\[transform\:translateZ\(0\)\] {
    transform: translateZ(0);
  }

  .\[transform\:translateZ\(30px\)\] {
    transform: translateZ(30px);
  }

  .transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .animate-bounce {
    animation: var(--animate-bounce);
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .resize {
    resize: both;
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .items-center {
    align-items: center;
  }

  .items-end {
    align-items: flex-end;
  }

  .justify-center {
    justify-content: center;
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }

  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }

  .rounded-\[4px\] {
    border-radius: 4px;
  }

  .rounded-\[15px\] {
    border-radius: 15px;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius);
  }

  .rounded-md {
    border-radius: calc(var(--radius)  - 2px);
  }

  .rounded-xl {
    border-radius: calc(var(--radius)  + 4px);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-t-4 {
    border-top-style: var(--tw-border-style);
    border-top-width: 4px;
  }

  .border-r-4 {
    border-right-style: var(--tw-border-style);
    border-right-width: 4px;
  }

  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }

  .border-gray-200 {
    border-color: var(--color-gray-200);
  }

  .border-gray-200\/50 {
    border-color: rgba(229, 231, 235, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-gray-200\/50 {
      border-color: color-mix(in oklab, var(--color-gray-200) 50%, transparent);
    }
  }

  .border-gray-300 {
    border-color: var(--color-gray-300);
  }

  .border-gray-300\/30 {
    border-color: rgba(209, 213, 220, .3);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-gray-300\/30 {
      border-color: color-mix(in oklab, var(--color-gray-300) 30%, transparent);
    }
  }

  .border-gray-300\/40 {
    border-color: rgba(209, 213, 220, .4);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-gray-300\/40 {
      border-color: color-mix(in oklab, var(--color-gray-300) 40%, transparent);
    }
  }

  .border-gray-300\/50 {
    border-color: rgba(209, 213, 220, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-gray-300\/50 {
      border-color: color-mix(in oklab, var(--color-gray-300) 50%, transparent);
    }
  }

  .border-gray-700 {
    border-color: var(--color-gray-700);
  }

  .border-white {
    border-color: var(--color-white);
  }

  .border-white\/10 {
    border-color: rgba(255, 255, 255, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-white\/10 {
      border-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .border-white\/20 {
    border-color: rgba(255, 255, 255, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-white\/20 {
      border-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  .border-white\/30 {
    border-color: rgba(255, 255, 255, .3);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-white\/30 {
      border-color: color-mix(in oklab, var(--color-white) 30%, transparent);
    }
  }

  .border-t-gray-900\/95 {
    border-top-color: rgba(16, 24, 40, .95);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-t-gray-900\/95 {
      border-top-color: color-mix(in oklab, var(--color-gray-900) 95%, transparent);
    }
  }

  .border-r-transparent {
    border-right-color: rgba(0, 0, 0, 0);
  }

  .border-l-transparent {
    border-left-color: rgba(0, 0, 0, 0);
  }

  .bg-black {
    background-color: var(--color-black);
  }

  .bg-black\/20 {
    background-color: rgba(0, 0, 0, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/20 {
      background-color: color-mix(in oklab, var(--color-black) 20%, transparent);
    }
  }

  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }

  .bg-gray-200\/80 {
    background-color: rgba(229, 231, 235, .8);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-gray-200\/80 {
      background-color: color-mix(in oklab, var(--color-gray-200) 80%, transparent);
    }
  }

  .bg-gray-900\/95 {
    background-color: rgba(16, 24, 40, .95);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-gray-900\/95 {
      background-color: color-mix(in oklab, var(--color-gray-900) 95%, transparent);
    }
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-white\/10 {
    background-color: rgba(255, 255, 255, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/10 {
      background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .bg-white\/80 {
    background-color: rgba(255, 255, 255, .8);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/80 {
      background-color: color-mix(in oklab, var(--color-white) 80%, transparent);
    }
  }

  .bg-white\/90 {
    background-color: rgba(255, 255, 255, .9);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/90 {
      background-color: color-mix(in oklab, var(--color-white) 90%, transparent);
    }
  }

  .bg-white\/95 {
    background-color: rgba(255, 255, 255, .95);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/95 {
      background-color: color-mix(in oklab, var(--color-white) 95%, transparent);
    }
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .from-black {
    --tw-gradient-from: var(--color-black);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-gray-900 {
    --tw-gradient-from: var(--color-gray-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-slate-50 {
    --tw-gradient-from: var(--color-slate-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-transparent {
    --tw-gradient-from: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .via-emerald-500 {
    --tw-gradient-via: var(--color-emerald-500);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-sky-500 {
    --tw-gradient-via: var(--color-sky-500);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .to-black {
    --tw-gradient-to: var(--color-black);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-gray-100 {
    --tw-gradient-to: var(--color-gray-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-gray-800 {
    --tw-gradient-to: var(--color-gray-800);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-transparent {
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .object-cover {
    object-fit: cover;
  }

  .object-center {
    object-position: center;
  }

  .object-top {
    object-position: top;
  }

  .\!p-0 {
    padding: calc(var(--spacing) * 0) !important;
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-10 {
    padding-inline: calc(var(--spacing) * 10);
  }

  .px-12 {
    padding-inline: calc(var(--spacing) * 12);
  }

  .px-\[10px\] {
    padding-left: 10px;
    padding-right: 10px;
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-5 {
    padding-block: calc(var(--spacing) * 5);
  }

  .py-\[4px\] {
    padding-top: 4px;
    padding-bottom: 4px;
  }

  .pt-16 {
    padding-top: calc(var(--spacing) * 16);
  }

  .pt-20 {
    padding-top: calc(var(--spacing) * 20);
  }

  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }

  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }

  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }

  .pb-32 {
    padding-bottom: calc(var(--spacing) * 32);
  }

  .text-center {
    text-align: center;
  }

  .font-sans {
    font-family: var(--font-geist-sans);
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-5xl {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
  }

  .text-6xl {
    font-size: var(--text-6xl);
    line-height: var(--tw-leading, var(--text-6xl--line-height));
  }

  .text-8xl {
    font-size: var(--text-8xl);
    line-height: var(--tw-leading, var(--text-8xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .text-\[10px\] {
    font-size: 10px;
  }

  .font-black {
    --tw-font-weight: var(--font-weight-black);
    font-weight: var(--font-weight-black);
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-\[-0\.02em\] {
    --tw-tracking: -.02em;
    letter-spacing: -.02em;
  }

  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }

  .tracking-wide {
    --tw-tracking: var(--tracking-wide);
    letter-spacing: var(--tracking-wide);
  }

  .tracking-wider {
    --tw-tracking: var(--tracking-wider);
    letter-spacing: var(--tracking-wider);
  }

  .tracking-widest {
    --tw-tracking: var(--tracking-widest);
    letter-spacing: var(--tracking-widest);
  }

  .whitespace-normal {
    white-space: normal;
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .whitespace-pre {
    white-space: pre;
  }

  .text-\[\#2d2d2d\] {
    color: #2d2d2d;
  }

  .text-black {
    color: var(--color-black);
  }

  .text-gray-200 {
    color: var(--color-gray-200);
  }

  .text-gray-300 {
    color: var(--color-gray-300);
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-700 {
    color: var(--color-gray-700);
  }

  .text-gray-800 {
    color: var(--color-gray-800);
  }

  .text-white {
    color: var(--color-white);
  }

  .text-yellow-500 {
    color: var(--color-yellow-500);
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .opacity-0 {
    opacity: 0;
  }

  .opacity-5 {
    opacity: .05;
  }

  .opacity-8 {
    opacity: .08;
  }

  .opacity-30 {
    opacity: .3;
  }

  .opacity-50 {
    opacity: .5;
  }

  .opacity-100 {
    opacity: 1;
  }

  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgba(0, 0, 0, .25));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 4px 6px -4px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 2px 4px -2px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 8px 10px -6px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .drop-shadow {
    --tw-drop-shadow-size: drop-shadow(0 1px 2px var(--tw-drop-shadow-color, rgba(0, 0, 0, .1))) drop-shadow(0 1px 1px var(--tw-drop-shadow-color, rgba(0, 0, 0, .06)));
    --tw-drop-shadow: drop-shadow(0 1px 2px rgba(0, 0, 0, .1)) drop-shadow(0 1px 1px rgba(0, 0, 0, .06));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .backdrop-blur-2xl {
    --tw-backdrop-blur: blur(var(--blur-2xl));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-md {
    --tw-backdrop-blur: blur(var(--blur-md));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-xl {
    --tw-backdrop-blur: blur(var(--blur-xl));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .delay-300 {
    transition-delay: .3s;
  }

  .delay-500 {
    transition-delay: .5s;
  }

  .delay-700 {
    transition-delay: .7s;
  }

  .delay-900 {
    transition-delay: .9s;
  }

  .delay-1100 {
    transition-delay: 1.1s;
  }

  .duration-150 {
    --tw-duration: .15s;
    transition-duration: .15s;
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .duration-500 {
    --tw-duration: .5s;
    transition-duration: .5s;
  }

  .duration-1000 {
    --tw-duration: 1s;
    transition-duration: 1s;
  }

  .duration-1200 {
    --tw-duration: 1.2s;
    transition-duration: 1.2s;
  }

  .ease-\[cubic-bezier\(0\,0\,0\,1\)\] {
    --tw-ease: cubic-bezier(0, 0, 0, 1);
    transition-timing-function: cubic-bezier(0, 0, 0, 1);
  }

  .will-change-transform {
    will-change: transform;
  }

  .delay-300 {
    --tw-animation-delay: .3s;
    animation-delay: .3s;
  }

  .delay-500 {
    --tw-animation-delay: .5s;
    animation-delay: .5s;
  }

  .delay-700 {
    --tw-animation-delay: .7s;
    animation-delay: .7s;
  }

  .delay-900 {
    --tw-animation-delay: calc(900 * 1ms);
    animation-delay: .9s;
  }

  .delay-1100 {
    --tw-animation-delay: calc(1100 * 1ms);
    animation-delay: 1.1s;
  }

  .\[backface-visibility\:hidden\] {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  .\[perspective\:800px\] {
    perspective: 800px;
  }

  .\[perspective\:1000px\] {
    perspective: 1000px;
  }

  .\[transform-style\:preserve-3d\] {
    transform-style: preserve-3d;
  }

  @media (hover: hover) {
    .group-hover\:z-30:is(:where(.group):hover *) {
      z-index: 30;
    }
  }

  @media (hover: hover) {
    .group-hover\:scale-105:is(:where(.group):hover *) {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:scale-110:is(:where(.group):hover *) {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:-translate-y-1:hover {
      --tw-translate-y: calc(var(--spacing) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .hover\:scale-105:hover {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-800:hover {
      background-color: var(--color-gray-800);
    }
  }

  @media (hover: hover) {
    .hover\:bg-white:hover {
      background-color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .hover\:bg-white\/95:hover {
      background-color: rgba(255, 255, 255, .95);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-white\/95:hover {
        background-color: color-mix(in oklab, var(--color-white) 95%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:from-gray-800:hover {
      --tw-gradient-from: var(--color-gray-800);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:to-black:hover {
      --tw-gradient-to: var(--color-black);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:to-gray-900:hover {
      --tw-gradient-to: var(--color-gray-900);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:shadow-2xl:hover {
      --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgba(0, 0, 0, .25));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-xl:hover {
      --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 8px 10px -6px var(--tw-shadow-color, rgba(0, 0, 0, .1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  .active\:scale-95:active {
    --tw-scale-x: 95%;
    --tw-scale-y: 95%;
    --tw-scale-z: 95%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  @media (min-width: 40rem) {
    .sm\:block {
      display: block;
    }
  }

  @media (min-width: 40rem) {
    .sm\:hidden {
      display: none;
    }
  }

  @media (min-width: 48rem) {
    .md\:h-80 {
      height: calc(var(--spacing) * 80);
    }
  }

  @media (min-width: 48rem) {
    .md\:w-80 {
      width: calc(var(--spacing) * 80);
    }
  }

  @media (min-width: 48rem) {
    .md\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:text-7xl {
      font-size: var(--text-7xl);
      line-height: var(--tw-leading, var(--text-7xl--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:text-8xl {
      font-size: var(--text-8xl);
      line-height: var(--tw-leading, var(--text-8xl--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:text-9xl {
      font-size: var(--text-9xl);
      line-height: var(--tw-leading, var(--text-9xl--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:text-xl {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:text-\[5rem\] {
      font-size: 5rem;
    }
  }

  @media (min-width: 48rem) {
    .md\:text-\[12rem\] {
      font-size: 12rem;
    }
  }

  @media (min-width: 48rem) {
    .md\:leading-\[5rem\] {
      --tw-leading: 5rem;
      line-height: 5rem;
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-8xl {
      font-size: var(--text-8xl);
      line-height: var(--tw-leading, var(--text-8xl--line-height));
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-9xl {
      font-size: var(--text-9xl);
      line-height: var(--tw-leading, var(--text-9xl--line-height));
    }
  }
}

@property --tw-animation-delay {
  syntax: "*";
  inherits: false;
  initial-value: 0s;
}

@property --tw-animation-direction {
  syntax: "*";
  inherits: false;
  initial-value: normal;
}

@property --tw-animation-duration {
  syntax: "*";
  inherits: false
}

@property --tw-animation-fill-mode {
  syntax: "*";
  inherits: false;
  initial-value: none;
}

@property --tw-animation-iteration-count {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-enter-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-enter-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-exit-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-exit-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

body {
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
  color: #171717 !important;
  background: #fff !important;
}

* {
  --lightningcss-light: initial !important;
  --lightningcss-dark:  !important;
  color-scheme: light !important;
}

.dark, [data-theme="dark"] {
  color: #171717 !important;
  background: #fff !important;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(.9);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-float {
  animation: 3s ease-in-out infinite float;
}

.animate-fadeInUp {
  animation: .8s ease-out forwards fadeInUp;
}

.animate-scaleIn {
  animation: .6s ease-out forwards scaleIn;
}

.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, .25);
}

.blur-text {
  white-space: nowrap;
  display: inline-block;
}

.blur-text span {
  white-space: nowrap;
}

h1, h2, h3, h4, h5, h6, p, span, div {
  color: inherit !important;
}

.professional-card {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -1px rgba(0, 0, 0, .06);
}

.professional-text {
  font-weight: 500;
  color: #334155 !important;
}

* {
  background-color: inherit;
  color: inherit;
}

:root {
  --radius: .625rem;
  --background: #fff;
  --foreground: #0a0a0a;
  --card: #fff;
  --card-foreground: #0a0a0a;
  --popover: #fff;
  --popover-foreground: #0a0a0a;
  --primary: #171717;
  --primary-foreground: #fafafa;
  --secondary: #f5f5f5;
  --secondary-foreground: #171717;
  --muted: #f5f5f5;
  --muted-foreground: #737373;
  --accent: #f5f5f5;
  --accent-foreground: #171717;
  --destructive: #e40014;
  --border: #e5e5e5;
  --input: #e5e5e5;
  --ring: #a1a1a1;
  --chart-1: #f05100;
  --chart-2: #009588;
  --chart-3: #104e64;
  --chart-4: #fcbb00;
  --chart-5: #f99c00;
  --sidebar: #fafafa;
  --sidebar-foreground: #0a0a0a;
  --sidebar-primary: #171717;
  --sidebar-primary-foreground: #fafafa;
  --sidebar-accent: #f5f5f5;
  --sidebar-accent-foreground: #171717;
  --sidebar-border: #e5e5e5;
  --sidebar-ring: #a1a1a1;
}

@supports (color: color(display-p3 0 0 0)) {
  :root {
    --background: color(display-p3 1 1 1);
    --foreground: color(display-p3 .0393882 .0393882 .0393882);
    --card: color(display-p3 1 1 1);
    --card-foreground: color(display-p3 .0393882 .0393882 .0393882);
    --popover: color(display-p3 1 1 1);
    --popover-foreground: color(display-p3 .0393882 .0393882 .0393882);
    --primary: color(display-p3 .0905274 .0905274 .0905274);
    --primary-foreground: color(display-p3 .980256 .980256 .980256);
    --secondary: color(display-p3 .960587 .960587 .960587);
    --secondary-foreground: color(display-p3 .0905274 .0905274 .0905274);
    --muted: color(display-p3 .960587 .960587 .960587);
    --muted-foreground: color(display-p3 .451519 .451519 .451519);
    --accent: color(display-p3 .960587 .960587 .960587);
    --accent-foreground: color(display-p3 .0905274 .0905274 .0905274);
    --destructive: color(display-p3 .830323 .140383 .133196);
    --border: color(display-p3 .898161 .898161 .898161);
    --input: color(display-p3 .898161 .898161 .898161);
    --ring: color(display-p3 .630163 .630163 .630163);
    --chart-1: color(display-p3 .887467 .341665 .0219962);
    --chart-2: color(display-p3 .207114 .579584 .53668);
    --chart-3: color(display-p3 .142586 .302008 .385094);
    --chart-4: color(display-p3 .959186 .738519 .118268);
    --chart-5: color(display-p3 .93994 .620584 .0585367);
    --sidebar: color(display-p3 .980256 .980256 .980256);
    --sidebar-foreground: color(display-p3 .0393882 .0393882 .0393882);
    --sidebar-primary: color(display-p3 .0905274 .0905274 .0905274);
    --sidebar-primary-foreground: color(display-p3 .980256 .980256 .980256);
    --sidebar-accent: color(display-p3 .960587 .960587 .960587);
    --sidebar-accent-foreground: color(display-p3 .0905274 .0905274 .0905274);
    --sidebar-border: color(display-p3 .898161 .898161 .898161);
    --sidebar-ring: color(display-p3 .630163 .630163 .630163);
  }
}

@supports (color: lab(0% 0 0)) {
  :root {
    --background: lab(100% 0 0);
    --foreground: lab(2.75381% 0 0);
    --card: lab(100% 0 0);
    --card-foreground: lab(2.75381% 0 0);
    --popover: lab(100% 0 0);
    --popover-foreground: lab(2.75381% 0 0);
    --primary: lab(7.78201% -.0000149012 0);
    --primary-foreground: lab(98.26% 0 0);
    --secondary: lab(96.52% -.0000596046 0);
    --secondary-foreground: lab(7.78201% -.0000149012 0);
    --muted: lab(96.52% -.0000596046 0);
    --muted-foreground: lab(48.496% 0 0);
    --accent: lab(96.52% -.0000596046 0);
    --accent-foreground: lab(7.78201% -.0000149012 0);
    --destructive: lab(48.4493% 77.4328 61.5452);
    --border: lab(90.952% -.0000596046 0);
    --input: lab(90.952% -.0000596046 0);
    --ring: lab(66.128% -.0000298023 .0000119209);
    --chart-1: lab(57.1026% 64.2584 89.8886);
    --chart-2: lab(55.0223% -41.0774 -3.90277);
    --chart-3: lab(30.372% -13.1853 -18.7887);
    --chart-4: lab(80.1641% 16.6016 99.2089);
    --chart-5: lab(72.7183% 31.8672 97.9407);
    --sidebar: lab(98.26% 0 0);
    --sidebar-foreground: lab(2.75381% 0 0);
    --sidebar-primary: lab(7.78201% -.0000149012 0);
    --sidebar-primary-foreground: lab(98.26% 0 0);
    --sidebar-accent: lab(96.52% -.0000596046 0);
    --sidebar-accent-foreground: lab(7.78201% -.0000149012 0);
    --sidebar-border: lab(90.952% -.0000596046 0);
    --sidebar-ring: lab(66.128% -.0000298023 .0000119209);
  }
}

.dark {
  --background: #0a0a0a;
  --foreground: #fafafa;
  --card: #171717;
  --card-foreground: #fafafa;
  --popover: #171717;
  --popover-foreground: #fafafa;
  --primary: #e5e5e5;
  --primary-foreground: #171717;
  --secondary: #262626;
  --secondary-foreground: #fafafa;
  --muted: #262626;
  --muted-foreground: #a1a1a1;
  --accent: #262626;
  --accent-foreground: #fafafa;
  --destructive: #ff6568;
  --border: rgba(255, 255, 255, .1);
  --input: rgba(255, 255, 255, .15);
  --ring: #737373;
  --chart-1: #1447e6;
  --chart-2: #00bb7f;
  --chart-3: #f99c00;
  --chart-4: #ac4bff;
  --chart-5: #ff2357;
  --sidebar: #171717;
  --sidebar-foreground: #fafafa;
  --sidebar-primary: #1447e6;
  --sidebar-primary-foreground: #fafafa;
  --sidebar-accent: #262626;
  --sidebar-accent-foreground: #fafafa;
  --sidebar-border: rgba(255, 255, 255, .1);
  --sidebar-ring: #737373;
}

@supports (color: color(display-p3 0 0 0)) {
  .dark {
    --background: color(display-p3 .0393882 .0393882 .0393882);
    --foreground: color(display-p3 .980256 .980256 .980256);
    --card: color(display-p3 .0905274 .0905274 .0905274);
    --card-foreground: color(display-p3 .980256 .980256 .980256);
    --popover: color(display-p3 .0905274 .0905274 .0905274);
    --popover-foreground: color(display-p3 .980256 .980256 .980256);
    --primary: color(display-p3 .898161 .898161 .898161);
    --primary-foreground: color(display-p3 .0905274 .0905274 .0905274);
    --secondary: color(display-p3 .149382 .149382 .149382);
    --secondary-foreground: color(display-p3 .980256 .980256 .980256);
    --muted: color(display-p3 .149382 .149382 .149382);
    --muted-foreground: color(display-p3 .630163 .630163 .630163);
    --accent: color(display-p3 .149382 .149382 .149382);
    --accent-foreground: color(display-p3 .980256 .980256 .980256);
    --destructive: color(display-p3 .933534 .431676 .423491);
    --border: color(display-p3 1 1 1 / .1);
    --input: color(display-p3 1 1 1 / .15);
    --ring: color(display-p3 .451519 .451519 .451519);
    --chart-1: color(display-p3 .1379 .274983 .867624);
    --chart-2: color(display-p3 .267113 .726847 .508397);
    --chart-3: color(display-p3 .93994 .620584 .0585367);
    --chart-4: color(display-p3 .629519 .30089 .990817);
    --chart-5: color(display-p3 .921824 .240748 .355666);
    --sidebar: color(display-p3 .0905274 .0905274 .0905274);
    --sidebar-foreground: color(display-p3 .980256 .980256 .980256);
    --sidebar-primary: color(display-p3 .1379 .274983 .867624);
    --sidebar-primary-foreground: color(display-p3 .980256 .980256 .980256);
    --sidebar-accent: color(display-p3 .149382 .149382 .149382);
    --sidebar-accent-foreground: color(display-p3 .980256 .980256 .980256);
    --sidebar-border: color(display-p3 1 1 1 / .1);
    --sidebar-ring: color(display-p3 .451519 .451519 .451519);
  }
}

@supports (color: lab(0% 0 0)) {
  .dark {
    --background: lab(2.75381% 0 0);
    --foreground: lab(98.26% 0 0);
    --card: lab(7.78201% -.0000149012 0);
    --card-foreground: lab(98.26% 0 0);
    --popover: lab(7.78201% -.0000149012 0);
    --popover-foreground: lab(98.26% 0 0);
    --primary: lab(90.952% -.0000596046 0);
    --primary-foreground: lab(7.78201% -.0000149012 0);
    --secondary: lab(15.204% 0 0);
    --secondary-foreground: lab(98.26% 0 0);
    --muted: lab(15.204% 0 0);
    --muted-foreground: lab(66.128% -.0000298023 .0000119209);
    --accent: lab(15.204% 0 0);
    --accent-foreground: lab(98.26% 0 0);
    --destructive: lab(63.7053% 60.7449 31.3109);
    --border: lab(100% 0 0 / .1);
    --input: lab(100% 0 0 / .15);
    --ring: lab(48.496% 0 0);
    --chart-1: lab(36.9089% 35.0961 -85.6872);
    --chart-2: lab(66.9756% -58.27 19.5419);
    --chart-3: lab(72.7183% 31.8672 97.9407);
    --chart-4: lab(52.0183% 66.11 -78.2316);
    --chart-5: lab(56.101% 79.4329 31.4532);
    --sidebar: lab(7.78201% -.0000149012 0);
    --sidebar-foreground: lab(98.26% 0 0);
    --sidebar-primary: lab(36.9089% 35.0961 -85.6872);
    --sidebar-primary-foreground: lab(98.26% 0 0);
    --sidebar-accent: lab(15.204% 0 0);
    --sidebar-accent-foreground: lab(98.26% 0 0);
    --sidebar-border: lab(100% 0 0 / .1);
    --sidebar-ring: lab(48.496% 0 0);
  }
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@keyframes bounce {
  0%, 100% {
    animation-timing-function: cubic-bezier(.8, 0, 1, 1);
    transform: translateY(-25%);
  }

  50% {
    animation-timing-function: cubic-bezier(0, 0, .2, 1);
    transform: none;
  }
}

/*# sourceMappingURL=app_globals_css_e59ae46c._.single.css.map*/