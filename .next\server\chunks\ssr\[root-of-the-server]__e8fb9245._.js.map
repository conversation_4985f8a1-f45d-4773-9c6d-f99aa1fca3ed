{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/main/components/SplitText/SplitText.tsx"], "sourcesContent": ["import React, { useRef, useEffect } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport { SplitText as GSAPSplitText } from \"gsap/SplitText\";\n\ngsap.registerPlugin(ScrollTrigger, GSAPSplitText);\n\nexport interface SplitTextProps {\n  text: string;\n  className?: string;\n  delay?: number;\n  duration?: number;\n  ease?: string | ((t: number) => number);\n  splitType?: \"chars\" | \"words\" | \"lines\" | \"words, chars\";\n  from?: gsap.TweenVars;\n  to?: gsap.TweenVars;\n  threshold?: number;\n  rootMargin?: string;\n  textAlign?: React.CSSProperties[\"textAlign\"];\n  onLetterAnimationComplete?: () => void;\n}\n\nconst SplitText: React.FC<SplitTextProps> = ({\n  text,\n  className = \"\",\n  delay = 100,\n  duration = 0.6,\n  ease = \"power3.out\",\n  splitType = \"chars\",\n  from = { opacity: 0, y: 40 },\n  to = { opacity: 1, y: 0 },\n  threshold = 0.1,\n  rootMargin = \"-100px\",\n  textAlign = \"center\",\n  onLetterAnimationComplete,\n}) => {\n  const ref = useRef<HTMLParagraphElement>(null);\n  const animationCompletedRef = useRef(false);\n  const scrollTriggerRef = useRef<ScrollTrigger | null>(null);\n\n  useEffect(() => {\n    if (typeof window === \"undefined\" || !ref.current || !text) return;\n\n    const el = ref.current;\n    \n    animationCompletedRef.current = false;\n\n    const absoluteLines = splitType === \"lines\";\n    if (absoluteLines) el.style.position = \"relative\";\n\n    let splitter: GSAPSplitText;\n    try {\n      splitter = new GSAPSplitText(el, {\n        type: splitType,\n        absolute: absoluteLines,\n        linesClass: \"split-line\",\n      });\n    } catch (error) {\n      console.error(\"Failed to create SplitText:\", error);\n      return;\n    }\n\n    let targets: Element[];\n    switch (splitType) {\n      case \"lines\":\n        targets = splitter.lines;\n        break;\n      case \"words\":\n        targets = splitter.words;\n        break;\n      case \"chars\":\n        targets = splitter.chars;\n        break;\n      default:\n        targets = splitter.chars;\n    }\n\n    if (!targets || targets.length === 0) {\n      console.warn(\"No targets found for SplitText animation\");\n      splitter.revert();\n      return;\n    }\n\n    targets.forEach((t) => {\n      (t as HTMLElement).style.willChange = \"transform, opacity\";\n    });\n\n    const startPct = (1 - threshold) * 100;\n    const marginMatch = /^(-?\\d+(?:\\.\\d+)?)(px|em|rem|%)?$/.exec(rootMargin);\n    const marginValue = marginMatch ? parseFloat(marginMatch[1]) : 0;\n    const marginUnit = marginMatch ? (marginMatch[2] || \"px\") : \"px\";\n    const sign = marginValue < 0 ? `-=${Math.abs(marginValue)}${marginUnit}` : `+=${marginValue}${marginUnit}`;\n    const start = `top ${startPct}%${sign}`;\n\n    const tl = gsap.timeline({\n      scrollTrigger: {\n        trigger: el,\n        start,\n        toggleActions: \"play none none none\",\n        once: true,\n        onToggle: (self) => {\n          scrollTriggerRef.current = self;\n        },\n      },\n      smoothChildTiming: true,\n      onComplete: () => {\n        animationCompletedRef.current = true;\n        gsap.set(targets, {\n          ...to,\n          clearProps: \"willChange\",\n          immediateRender: true,\n        });\n        onLetterAnimationComplete?.();\n      },\n    });\n\n    tl.set(targets, { ...from, immediateRender: false, force3D: true });\n    tl.to(targets, {\n      ...to,\n      duration,\n      ease,\n      stagger: delay / 1000,\n      force3D: true,\n    });\n\n    return () => {\n      tl.kill();\n      if (scrollTriggerRef.current) {\n        scrollTriggerRef.current.kill();\n        scrollTriggerRef.current = null;\n      }\n      gsap.killTweensOf(targets);\n      if (splitter) {\n        splitter.revert();\n      }\n    };\n  }, [\n    text,\n    delay,\n    duration,\n    ease,\n    splitType,\n    from,\n    to,\n    threshold,\n    rootMargin,\n    onLetterAnimationComplete,\n  ]);\n\n  return (\n    <p\n      ref={ref}\n      className={`split-parent overflow-hidden inline-block whitespace-normal ${className}`}\n      style={{\n        textAlign,\n        wordWrap: \"break-word\",\n      }}\n    >\n      {text}\n    </p>\n  );\n};\n\nexport default SplitText;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,6IAAA,CAAA,OAAI,CAAC,cAAc,CAAC,qIAAA,CAAA,gBAAa,EAAE,iIAAA,CAAA,YAAa;AAiBhD,MAAM,YAAsC,CAAC,EAC3C,IAAI,EACJ,YAAY,EAAE,EACd,QAAQ,GAAG,EACX,WAAW,GAAG,EACd,OAAO,YAAY,EACnB,YAAY,OAAO,EACnB,OAAO;IAAE,SAAS;IAAG,GAAG;AAAG,CAAC,EAC5B,KAAK;IAAE,SAAS;IAAG,GAAG;AAAE,CAAC,EACzB,YAAY,GAAG,EACf,aAAa,QAAQ,EACrB,YAAY,QAAQ,EACpB,yBAAyB,EAC1B;IACC,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAwB;IACzC,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACrC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAwB;IAEtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAA4D;;;QAE5D,MAAM;QAIN,MAAM;QAGN,IAAI;QAYJ,IAAI;QAyBJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QAEN,MAAM;IA0CR,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAC,4DAA4D,EAAE,WAAW;QACrF,OAAO;YACL;YACA,UAAU;QACZ;kBAEC;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/main/components/ScrollVelocity/ScrollVelocity.tsx"], "sourcesContent": ["import React, { useRef, useLayoutEffect, useState } from \"react\";\nimport {\n  motion,\n  useScroll,\n  useSpring,\n  useTransform,\n  useMotionValue,\n  useVelocity,\n  useAnimationFrame,\n} from \"framer-motion\";\n\ninterface VelocityMapping {\n  input: [number, number];\n  output: [number, number];\n}\n\ninterface VelocityTextProps {\n  children: React.ReactNode;\n  baseVelocity: number;\n  scrollContainerRef?: React.RefObject<HTMLElement>;\n  className?: string;\n  damping?: number;\n  stiffness?: number;\n  numCopies?: number;\n  velocityMapping?: VelocityMapping;\n  parallaxClassName?: string;\n  scrollerClassName?: string;\n  parallaxStyle?: React.CSSProperties;\n  scrollerStyle?: React.CSSProperties;\n}\n\ninterface ScrollVelocityProps {\n  scrollContainerRef?: React.RefObject<HTMLElement>;\n  texts: string[];\n  velocity?: number;\n  className?: string;\n  damping?: number;\n  stiffness?: number;\n  numCopies?: number;\n  velocityMapping?: VelocityMapping;\n  parallaxClassName?: string;\n  scrollerClassName?: string;\n  parallaxStyle?: React.CSSProperties;\n  scrollerStyle?: React.CSSProperties;\n}\n\nfunction useElementWidth<T extends HTMLElement>(ref: React.RefObject<T | null>): number {\n  const [width, setWidth] = useState(0);\n\n  useLayoutEffect(() => {\n    function updateWidth() {\n      if (ref.current) {\n        setWidth(ref.current.offsetWidth);\n      }\n    }\n    updateWidth();\n    window.addEventListener(\"resize\", updateWidth);\n    return () => window.removeEventListener(\"resize\", updateWidth);\n  }, [ref]);\n\n  return width;\n}\n\nexport const ScrollVelocity: React.FC<ScrollVelocityProps> = ({\n  scrollContainerRef,\n  texts = [],\n  velocity = 100,\n  className = \"\",\n  damping = 50,\n  stiffness = 400,\n  numCopies = 6,\n  velocityMapping = { input: [0, 1000], output: [0, 5] },\n  parallaxClassName,\n  scrollerClassName,\n  parallaxStyle,\n  scrollerStyle,\n}) => {\n  function VelocityText({\n    children,\n    baseVelocity = velocity,\n    scrollContainerRef,\n    className = \"\",\n    damping,\n    stiffness,\n    numCopies,\n    velocityMapping,\n    parallaxClassName,\n    scrollerClassName,\n    parallaxStyle,\n    scrollerStyle,\n  }: VelocityTextProps) {\n    const baseX = useMotionValue(0);\n    const scrollOptions = scrollContainerRef\n      ? { container: scrollContainerRef }\n      : {};\n    const { scrollY } = useScroll(scrollOptions);\n    const scrollVelocity = useVelocity(scrollY);\n    const smoothVelocity = useSpring(scrollVelocity, {\n      damping: damping ?? 50,\n      stiffness: stiffness ?? 400,\n    });\n    const velocityFactor = useTransform(\n      smoothVelocity,\n      velocityMapping?.input || [0, 1000],\n      velocityMapping?.output || [0, 5],\n      { clamp: false }\n    );\n\n    const copyRef = useRef<HTMLSpanElement>(null);\n    const copyWidth = useElementWidth(copyRef);\n\n    function wrap(min: number, max: number, v: number): number {\n      const range = max - min;\n      const mod = (((v - min) % range) + range) % range;\n      return mod + min;\n    }\n\n    const x = useTransform(baseX, (v) => {\n      if (copyWidth === 0) return \"0px\";\n      return `${wrap(-copyWidth, 0, v)}px`;\n    });\n\n    const directionFactor = useRef<number>(1);\n    useAnimationFrame((t, delta) => {\n      let moveBy = directionFactor.current * baseVelocity * (delta / 1000);\n\n      if (velocityFactor.get() < 0) {\n        directionFactor.current = -1;\n      } else if (velocityFactor.get() > 0) {\n        directionFactor.current = 1;\n      }\n\n      moveBy += directionFactor.current * moveBy * velocityFactor.get();\n      baseX.set(baseX.get() + moveBy);\n    });\n\n    const spans = [];\n    for (let i = 0; i < numCopies!; i++) {\n      spans.push(\n        <span\n          className={`flex-shrink-0 ${className}`}\n          key={i}\n          ref={i === 0 ? copyRef : null}\n        >\n          {children}\n        </span>\n      );\n    }\n\n    return (\n      <div\n        className={`${parallaxClassName} relative overflow-hidden`}\n        style={parallaxStyle}\n      >\n        <motion.div\n          className={`${scrollerClassName} flex whitespace-nowrap text-center font-sans text-4xl font-bold tracking-[-0.02em] drop-shadow md:text-[5rem] md:leading-[5rem]`}\n          style={{ x, ...scrollerStyle }}\n        >\n          {spans}\n        </motion.div>\n      </div>\n    );\n  }\n\n  return (\n    <section>\n      {texts.map((text: string, index: number) => (\n        <VelocityText\n          key={index}\n          className={className}\n          baseVelocity={index % 2 !== 0 ? -velocity : velocity}\n          scrollContainerRef={scrollContainerRef}\n          damping={damping}\n          stiffness={stiffness}\n          numCopies={numCopies}\n          velocityMapping={velocityMapping}\n          parallaxClassName={parallaxClassName}\n          scrollerClassName={scrollerClassName}\n          parallaxStyle={parallaxStyle}\n          scrollerStyle={scrollerStyle}\n        >\n          {text}&nbsp;\n        </VelocityText>\n      ))}\n    </section>\n  );\n};\n\nexport default ScrollVelocity;\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AA6CA,SAAS,gBAAuC,GAA8B;IAC5E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,qMAAA,CAAA,kBAAe,AAAD,EAAE;QACd,SAAS;YACP,IAAI,IAAI,OAAO,EAAE;gBACf,SAAS,IAAI,OAAO,CAAC,WAAW;YAClC;QACF;QACA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;KAAI;IAER,OAAO;AACT;AAEO,MAAM,iBAAgD,CAAC,EAC5D,kBAAkB,EAClB,QAAQ,EAAE,EACV,WAAW,GAAG,EACd,YAAY,EAAE,EACd,UAAU,EAAE,EACZ,YAAY,GAAG,EACf,YAAY,CAAC,EACb,kBAAkB;IAAE,OAAO;QAAC;QAAG;KAAK;IAAE,QAAQ;QAAC;QAAG;KAAE;AAAC,CAAC,EACtD,iBAAiB,EACjB,iBAAiB,EACjB,aAAa,EACb,aAAa,EACd;IACC,SAAS,aAAa,EACpB,QAAQ,EACR,eAAe,QAAQ,EACvB,kBAAkB,EAClB,YAAY,EAAE,EACd,OAAO,EACP,SAAS,EACT,SAAS,EACT,eAAe,EACf,iBAAiB,EACjB,iBAAiB,EACjB,aAAa,EACb,aAAa,EACK;QAClB,MAAM,QAAQ,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;QAC7B,MAAM,gBAAgB,qBAClB;YAAE,WAAW;QAAmB,IAChC,CAAC;QACL,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,MAAM,iBAAiB,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QACnC,MAAM,iBAAiB,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB;YAC/C,SAAS,WAAW;YACpB,WAAW,aAAa;QAC1B;QACA,MAAM,iBAAiB,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAChC,gBACA,iBAAiB,SAAS;YAAC;YAAG;SAAK,EACnC,iBAAiB,UAAU;YAAC;YAAG;SAAE,EACjC;YAAE,OAAO;QAAM;QAGjB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAmB;QACxC,MAAM,YAAY,gBAAgB;QAElC,SAAS,KAAK,GAAW,EAAE,GAAW,EAAE,CAAS;YAC/C,MAAM,QAAQ,MAAM;YACpB,MAAM,MAAM,CAAC,AAAC,CAAC,IAAI,GAAG,IAAI,QAAS,KAAK,IAAI;YAC5C,OAAO,MAAM;QACf;QAEA,MAAM,IAAI,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,OAAO,CAAC;YAC7B,IAAI,cAAc,GAAG,OAAO;YAC5B,OAAO,GAAG,KAAK,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;QACtC;QAEA,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAU;QACvC,CAAA,GAAA,qLAAA,CAAA,oBAAiB,AAAD,EAAE,CAAC,GAAG;YACpB,IAAI,SAAS,gBAAgB,OAAO,GAAG,eAAe,CAAC,QAAQ,IAAI;YAEnE,IAAI,eAAe,GAAG,KAAK,GAAG;gBAC5B,gBAAgB,OAAO,GAAG,CAAC;YAC7B,OAAO,IAAI,eAAe,GAAG,KAAK,GAAG;gBACnC,gBAAgB,OAAO,GAAG;YAC5B;YAEA,UAAU,gBAAgB,OAAO,GAAG,SAAS,eAAe,GAAG;YAC/D,MAAM,GAAG,CAAC,MAAM,GAAG,KAAK;QAC1B;QAEA,MAAM,QAAQ,EAAE;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAY,IAAK;YACnC,MAAM,IAAI,eACR,8OAAC;gBACC,WAAW,CAAC,cAAc,EAAE,WAAW;gBAEvC,KAAK,MAAM,IAAI,UAAU;0BAExB;eAHI;;;;;QAMX;QAEA,qBACE,8OAAC;YACC,WAAW,GAAG,kBAAkB,yBAAyB,CAAC;YAC1D,OAAO;sBAEP,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,GAAG,kBAAkB,gIAAgI,CAAC;gBACjK,OAAO;oBAAE;oBAAG,GAAG,aAAa;gBAAC;0BAE5B;;;;;;;;;;;IAIT;IAEA,qBACE,8OAAC;kBACE,MAAM,GAAG,CAAC,CAAC,MAAc,sBACxB,8OAAC;gBAEC,WAAW;gBACX,cAAc,QAAQ,MAAM,IAAI,CAAC,WAAW;gBAC5C,oBAAoB;gBACpB,SAAS;gBACT,WAAW;gBACX,WAAW;gBACX,iBAAiB;gBACjB,mBAAmB;gBACnB,mBAAmB;gBACnB,eAAe;gBACf,eAAe;;oBAEd;oBAAK;;eAbD;;;;;;;;;;AAkBf;uCAEe", "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/main/components/BlurText/BlurText.tsx"], "sourcesContent": ["import { motion, Transition } from \"framer-motion\";\nimport { useEffect, useRef, useState, useMemo } from \"react\";\n\ntype BlurTextProps = {\n  text?: string;\n  delay?: number;\n  className?: string;\n  animateBy?: \"words\" | \"letters\";\n  direction?: \"top\" | \"bottom\";\n  threshold?: number;\n  rootMargin?: string;\n  animationFrom?: Record<string, string | number>;\n  animationTo?: Array<Record<string, string | number>>;\n  easing?: (t: number) => number;\n  onAnimationComplete?: () => void;\n  stepDuration?: number;\n};\n\nconst buildKeyframes = (\n  from: Record<string, string | number>,\n  steps: Array<Record<string, string | number>>\n): Record<string, Array<string | number>> => {\n  const keys = new Set<string>([\n    ...Object.keys(from),\n    ...steps.flatMap((s) => Object.keys(s)),\n  ]);\n\n  const keyframes: Record<string, Array<string | number>> = {};\n  keys.forEach((k) => {\n    keyframes[k] = [from[k], ...steps.map((s) => s[k])];\n  });\n  return keyframes;\n};\n\nconst BlurText: React.FC<BlurTextProps> = ({\n  text = \"\",\n  delay = 200,\n  className = \"\",\n  animateBy = \"words\",\n  direction = \"top\",\n  threshold = 0.1,\n  rootMargin = \"0px\",\n  animationFrom,\n  animationTo,\n  easing = (t) => t,\n  onAnimationComplete,\n  stepDuration = 0.35,\n}) => {\n  const elements = animateBy === \"words\" ? text.split(\" \") : text.split(\"\");\n  const [inView, setInView] = useState(false);\n  const ref = useRef<HTMLParagraphElement>(null);\n\n  useEffect(() => {\n    if (!ref.current) return;\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setInView(true);\n          observer.unobserve(ref.current as Element);\n        }\n      },\n      { threshold, rootMargin }\n    );\n    observer.observe(ref.current);\n    return () => observer.disconnect();\n  }, [threshold, rootMargin]);\n\n  const defaultFrom = useMemo(\n    () =>\n      direction === \"top\"\n        ? { filter: \"blur(10px)\", opacity: 0, y: -50 }\n        : { filter: \"blur(10px)\", opacity: 0, y: 50 },\n    [direction]\n  );\n\n  const defaultTo = useMemo(\n    () => [\n      {\n        filter: \"blur(5px)\",\n        opacity: 0.5,\n        y: direction === \"top\" ? 5 : -5,\n      },\n      { filter: \"blur(0px)\", opacity: 1, y: 0 },\n    ],\n    [direction]\n  );\n\n  const fromSnapshot = animationFrom ?? defaultFrom;\n  const toSnapshots = animationTo ?? defaultTo;\n\n  const stepCount = toSnapshots.length + 1;\n  const totalDuration = stepDuration * (stepCount - 1);\n  const times = Array.from({ length: stepCount }, (_, i) =>\n    stepCount === 1 ? 0 : i / (stepCount - 1)\n  );\n\n  return (\n    <p ref={ref} className={`blur-text ${className} flex flex-wrap`}>\n      {elements.map((segment, index) => {\n        const animateKeyframes = buildKeyframes(fromSnapshot, toSnapshots);\n\n        const spanTransition: Transition = {\n          duration: totalDuration,\n          times,\n          delay: (index * delay) / 1000,\n        };\n        (spanTransition as any).ease = easing;\n\n        return (\n          <motion.span\n            key={index}\n            initial={fromSnapshot}\n            animate={inView ? animateKeyframes : fromSnapshot}\n            transition={spanTransition}\n            onAnimationComplete={\n              index === elements.length - 1 ? onAnimationComplete : undefined\n            }\n            style={{\n              display: \"inline-block\",\n              willChange: \"transform, filter, opacity\",\n            }}\n          >\n            {segment === \" \" ? \"\\u00A0\" : segment}\n            {animateBy === \"words\" && index < elements.length - 1 && \"\\u00A0\"}\n          </motion.span>\n        );\n      })}\n    </p>\n  );\n};\n\nexport default BlurText;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAiBA,MAAM,iBAAiB,CACrB,MACA;IAEA,MAAM,OAAO,IAAI,IAAY;WACxB,OAAO,IAAI,CAAC;WACZ,MAAM,OAAO,CAAC,CAAC,IAAM,OAAO,IAAI,CAAC;KACrC;IAED,MAAM,YAAoD,CAAC;IAC3D,KAAK,OAAO,CAAC,CAAC;QACZ,SAAS,CAAC,EAAE,GAAG;YAAC,IAAI,CAAC,EAAE;eAAK,MAAM,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE;SAAE;IACrD;IACA,OAAO;AACT;AAEA,MAAM,WAAoC,CAAC,EACzC,OAAO,EAAE,EACT,QAAQ,GAAG,EACX,YAAY,EAAE,EACd,YAAY,OAAO,EACnB,YAAY,KAAK,EACjB,YAAY,GAAG,EACf,aAAa,KAAK,EAClB,aAAa,EACb,WAAW,EACX,SAAS,CAAC,IAAM,CAAC,EACjB,mBAAmB,EACnB,eAAe,IAAI,EACpB;IACC,MAAM,WAAW,cAAc,UAAU,KAAK,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC;IACtE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAwB;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,IAAI,OAAO,EAAE;QAClB,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,UAAU;gBACV,SAAS,SAAS,CAAC,IAAI,OAAO;YAChC;QACF,GACA;YAAE;YAAW;QAAW;QAE1B,SAAS,OAAO,CAAC,IAAI,OAAO;QAC5B,OAAO,IAAM,SAAS,UAAU;IAClC,GAAG;QAAC;QAAW;KAAW;IAE1B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACxB,IACE,cAAc,QACV;YAAE,QAAQ;YAAc,SAAS;YAAG,GAAG,CAAC;QAAG,IAC3C;YAAE,QAAQ;YAAc,SAAS;YAAG,GAAG;QAAG,GAChD;QAAC;KAAU;IAGb,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACtB,IAAM;YACJ;gBACE,QAAQ;gBACR,SAAS;gBACT,GAAG,cAAc,QAAQ,IAAI,CAAC;YAChC;YACA;gBAAE,QAAQ;gBAAa,SAAS;gBAAG,GAAG;YAAE;SACzC,EACD;QAAC;KAAU;IAGb,MAAM,eAAe,iBAAiB;IACtC,MAAM,cAAc,eAAe;IAEnC,MAAM,YAAY,YAAY,MAAM,GAAG;IACvC,MAAM,gBAAgB,eAAe,CAAC,YAAY,CAAC;IACnD,MAAM,QAAQ,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAU,GAAG,CAAC,GAAG,IAClD,cAAc,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC;IAG1C,qBACE,8OAAC;QAAE,KAAK;QAAK,WAAW,CAAC,UAAU,EAAE,UAAU,eAAe,CAAC;kBAC5D,SAAS,GAAG,CAAC,CAAC,SAAS;YACtB,MAAM,mBAAmB,eAAe,cAAc;YAEtD,MAAM,iBAA6B;gBACjC,UAAU;gBACV;gBACA,OAAO,AAAC,QAAQ,QAAS;YAC3B;YACC,eAAuB,IAAI,GAAG;YAE/B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gBAEV,SAAS;gBACT,SAAS,SAAS,mBAAmB;gBACrC,YAAY;gBACZ,qBACE,UAAU,SAAS,MAAM,GAAG,IAAI,sBAAsB;gBAExD,OAAO;oBACL,SAAS;oBACT,YAAY;gBACd;;oBAEC,YAAY,MAAM,WAAW;oBAC7B,cAAc,WAAW,QAAQ,SAAS,MAAM,GAAG,KAAK;;eAbpD;;;;;QAgBX;;;;;;AAGN;uCAEe", "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/main/components/TiltedCard/TiltedCard.tsx"], "sourcesContent": ["import type { SpringOptions } from \"framer-motion\";\nimport { useRef, useState } from \"react\";\nimport { motion, useMotionValue, useSpring } from \"framer-motion\";\n\ninterface TiltedCardProps {\n  imageSrc: React.ComponentProps<\"img\">[\"src\"];\n  altText?: string;\n  captionText?: string;\n  containerHeight?: React.CSSProperties['height'];\n  containerWidth?: React.CSSProperties['width'];\n  imageHeight?: React.CSSProperties['height'];\n  imageWidth?: React.CSSProperties['width'];\n  scaleOnHover?: number;\n  rotateAmplitude?: number;\n  showMobileWarning?: boolean;\n  showTooltip?: boolean;\n  overlayContent?: React.ReactNode;\n  displayOverlayContent?: boolean;\n}\n\nconst springValues: SpringOptions = {\n  damping: 30,\n  stiffness: 100,\n  mass: 2,\n};\n\nexport default function TiltedCard({\n  imageSrc,\n  altText = \"Tilted card image\",\n  captionText = \"\",\n  containerHeight = \"300px\",\n  containerWidth = \"100%\",\n  imageHeight = \"300px\",\n  imageWidth = \"300px\",\n  scaleOnHover = 1.1,\n  rotateAmplitude = 14,\n  showMobileWarning = true,\n  showTooltip = true,\n  overlayContent = null,\n  displayOverlayContent = false,\n}: TiltedCardProps) {\n  const ref = useRef<HTMLElement>(null);\n  const x = useMotionValue(0);\n  const y = useMotionValue(0);\n  const rotateX = useSpring(useMotionValue(0), springValues);\n  const rotateY = useSpring(useMotionValue(0), springValues);\n  const scale = useSpring(1, springValues);\n  const opacity = useSpring(0);\n  const rotateFigcaption = useSpring(0, {\n    stiffness: 350,\n    damping: 30,\n    mass: 1,\n  });\n\n  const [lastY, setLastY] = useState(0);\n\n  function handleMouse(e: React.MouseEvent<HTMLElement>) {\n    if (!ref.current) return;\n\n    const rect = ref.current.getBoundingClientRect();\n    const offsetX = e.clientX - rect.left - rect.width / 2;\n    const offsetY = e.clientY - rect.top - rect.height / 2;\n\n    const rotationX = (offsetY / (rect.height / 2)) * -rotateAmplitude;\n    const rotationY = (offsetX / (rect.width / 2)) * rotateAmplitude;\n\n    rotateX.set(rotationX);\n    rotateY.set(rotationY);\n\n    x.set(e.clientX - rect.left);\n    y.set(e.clientY - rect.top);\n\n    const velocityY = offsetY - lastY;\n    rotateFigcaption.set(-velocityY * 0.6);\n    setLastY(offsetY);\n  }\n\n  function handleMouseEnter() {\n    scale.set(scaleOnHover);\n    opacity.set(1);\n  }\n\n  function handleMouseLeave() {\n    opacity.set(0);\n    scale.set(1);\n    rotateX.set(0);\n    rotateY.set(0);\n    rotateFigcaption.set(0);\n  }\n\n  return (\n    <figure\n      ref={ref}\n      className=\"relative w-full h-full [perspective:800px] flex flex-col items-center justify-center\"\n      style={{\n        height: containerHeight,\n        width: containerWidth,\n      }}\n      onMouseMove={handleMouse}\n      onMouseEnter={handleMouseEnter}\n      onMouseLeave={handleMouseLeave}\n    >\n      {showMobileWarning && (\n        <div className=\"absolute top-4 text-center text-sm block sm:hidden\">\n          This effect is not optimized for mobile. Check on desktop.\n        </div>\n      )}\n\n      <motion.div\n        className=\"relative [transform-style:preserve-3d]\"\n        style={{\n          width: imageWidth,\n          height: imageHeight,\n          rotateX,\n          rotateY,\n          scale,\n        }}\n      >\n        <motion.img\n          src={imageSrc}\n          alt={altText}\n          className=\"absolute top-0 left-0 object-cover rounded-[15px] will-change-transform [transform:translateZ(0)]\"\n          style={{\n            width: imageWidth,\n            height: imageHeight,\n          }}\n        />\n\n        {displayOverlayContent && overlayContent && (\n          <motion.div\n            className=\"absolute top-0 left-0 z-[2] will-change-transform [transform:translateZ(30px)]\"\n          >\n            {overlayContent}\n          </motion.div>\n        )}\n      </motion.div>\n\n      {showTooltip && (\n        <motion.figcaption\n          className=\"pointer-events-none absolute left-0 top-0 rounded-[4px] bg-white px-[10px] py-[4px] text-[10px] text-[#2d2d2d] opacity-0 z-[3] hidden sm:block\"\n          style={{\n            x,\n            y,\n            opacity,\n            rotate: rotateFigcaption,\n          }}\n        >\n          {captionText}\n        </motion.figcaption>\n      )}\n    </figure>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;;;;AAkBA,MAAM,eAA8B;IAClC,SAAS;IACT,WAAW;IACX,MAAM;AACR;AAEe,SAAS,WAAW,EACjC,QAAQ,EACR,UAAU,mBAAmB,EAC7B,cAAc,EAAE,EAChB,kBAAkB,OAAO,EACzB,iBAAiB,MAAM,EACvB,cAAc,OAAO,EACrB,aAAa,OAAO,EACpB,eAAe,GAAG,EAClB,kBAAkB,EAAE,EACpB,oBAAoB,IAAI,EACxB,cAAc,IAAI,EAClB,iBAAiB,IAAI,EACrB,wBAAwB,KAAK,EACb;IAChB,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IAChC,MAAM,IAAI,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IACzB,MAAM,IAAI,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IACzB,MAAM,UAAU,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE,IAAI;IAC7C,MAAM,UAAU,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE,IAAI;IAC7C,MAAM,QAAQ,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,GAAG;IAC3B,MAAM,UAAU,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE;IAC1B,MAAM,mBAAmB,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,GAAG;QACpC,WAAW;QACX,SAAS;QACT,MAAM;IACR;IAEA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,SAAS,YAAY,CAAgC;QACnD,IAAI,CAAC,IAAI,OAAO,EAAE;QAElB,MAAM,OAAO,IAAI,OAAO,CAAC,qBAAqB;QAC9C,MAAM,UAAU,EAAE,OAAO,GAAG,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;QACrD,MAAM,UAAU,EAAE,OAAO,GAAG,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;QAErD,MAAM,YAAY,AAAC,UAAU,CAAC,KAAK,MAAM,GAAG,CAAC,IAAK,CAAC;QACnD,MAAM,YAAY,AAAC,UAAU,CAAC,KAAK,KAAK,GAAG,CAAC,IAAK;QAEjD,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QAEZ,EAAE,GAAG,CAAC,EAAE,OAAO,GAAG,KAAK,IAAI;QAC3B,EAAE,GAAG,CAAC,EAAE,OAAO,GAAG,KAAK,GAAG;QAE1B,MAAM,YAAY,UAAU;QAC5B,iBAAiB,GAAG,CAAC,CAAC,YAAY;QAClC,SAAS;IACX;IAEA,SAAS;QACP,MAAM,GAAG,CAAC;QACV,QAAQ,GAAG,CAAC;IACd;IAEA,SAAS;QACP,QAAQ,GAAG,CAAC;QACZ,MAAM,GAAG,CAAC;QACV,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,iBAAiB,GAAG,CAAC;IACvB;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,OAAO;YACL,QAAQ;YACR,OAAO;QACT;QACA,aAAa;QACb,cAAc;QACd,cAAc;;YAEb,mCACC,8OAAC;gBAAI,WAAU;0BAAqD;;;;;;0BAKtE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,OAAO;oBACP,QAAQ;oBACR;oBACA;oBACA;gBACF;;kCAEA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,KAAK;wBACL,KAAK;wBACL,WAAU;wBACV,OAAO;4BACL,OAAO;4BACP,QAAQ;wBACV;;;;;;oBAGD,yBAAyB,gCACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;kCAET;;;;;;;;;;;;YAKN,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,UAAU;gBAChB,WAAU;gBACV,OAAO;oBACL;oBACA;oBACA;oBACA,QAAQ;gBACV;0BAEC;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/main/components/CircularText/CircularText.tsx"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport {\n  motion,\n  useAnimation,\n  useMotionValue,\n  MotionValue,\n  Transition,\n} from \"framer-motion\";\ninterface CircularTextProps {\n  text: string;\n  spinDuration?: number;\n  onHover?: \"slowDown\" | \"speedUp\" | \"pause\" | \"goBonkers\";\n  className?: string;\n}\n\nconst getRotationTransition = (\n  duration: number,\n  from: number,\n  loop: boolean = true\n) => ({\n  from,\n  to: from + 360,\n  ease: \"linear\" as const,\n  duration,\n  type: \"tween\" as const,\n  repeat: loop ? Infinity : 0,\n});\n\nconst getTransition = (duration: number, from: number) => ({\n  rotate: getRotationTransition(duration, from),\n  scale: {\n    type: \"spring\" as const,\n    damping: 20,\n    stiffness: 300,\n  },\n});\n\nconst CircularText: React.FC<CircularTextProps> = ({\n  text,\n  spinDuration = 20,\n  onHover = \"speedUp\",\n  className = \"\",\n}) => {\n  const letters = Array.from(text);\n  const controls = useAnimation();\n  const rotation: MotionValue<number> = useMotionValue(0);\n\n  useEffect(() => {\n    const start = rotation.get();\n    controls.start({\n      rotate: start + 360,\n      scale: 1,\n      transition: getTransition(spinDuration, start),\n    });\n  }, [spinDuration, text, onHover, controls]);\n\n  const handleHoverStart = () => {\n    const start = rotation.get();\n\n    if (!onHover) return;\n\n    let transitionConfig: ReturnType<typeof getTransition> | Transition;\n    let scaleVal = 1;\n\n    switch (onHover) {\n      case \"slowDown\":\n        transitionConfig = getTransition(spinDuration * 2, start);\n        break;\n      case \"speedUp\":\n        transitionConfig = getTransition(spinDuration / 4, start);\n        break;\n      case \"pause\":\n        transitionConfig = {\n          rotate: { type: \"spring\", damping: 20, stiffness: 300 },\n          scale: { type: \"spring\", damping: 20, stiffness: 300 },\n        };\n        break;\n      case \"goBonkers\":\n        transitionConfig = getTransition(spinDuration / 20, start);\n        scaleVal = 0.8;\n        break;\n      default:\n        transitionConfig = getTransition(spinDuration, start);\n    }\n\n    controls.start({\n      rotate: start + 360,\n      scale: scaleVal,\n      transition: transitionConfig,\n    });\n  };\n\n  const handleHoverEnd = () => {\n    const start = rotation.get();\n    controls.start({\n      rotate: start + 360,\n      scale: 1,\n      transition: getTransition(spinDuration, start),\n    });\n  };\n\n  return (\n    <motion.div\n    className={`m-0 mx-auto rounded-full w-[200px] h-[200px] relative font-black text-center cursor-pointer origin-center ${className}`}\n    style={{ rotate: rotation }}\n      initial={{ rotate: 0 }}\n      animate={controls}\n      onMouseEnter={handleHoverStart}\n      onMouseLeave={handleHoverEnd}\n    >\n      {letters.map((letter, i) => {\n        const rotationDeg = (360 / letters.length) * i;\n        const factor = Math.PI / letters.length;\n        const x = factor * i;\n        const y = factor * i;\n        const transform = `rotateZ(${rotationDeg}deg) translate3d(${x}px, ${y}px, 0)`;\n\n        return (\n          <span\n            key={i}\n            className=\"absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]\"\n            style={{ transform, WebkitTransform: transform }}\n          >\n            {letter}\n          </span>\n        );\n      })}\n    </motion.div>\n  );\n};\n\nexport default CircularText;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;;;;AAcA,MAAM,wBAAwB,CAC5B,UACA,MACA,OAAgB,IAAI,GACjB,CAAC;QACJ;QACA,IAAI,OAAO;QACX,MAAM;QACN;QACA,MAAM;QACN,QAAQ,OAAO,WAAW;IAC5B,CAAC;AAED,MAAM,gBAAgB,CAAC,UAAkB,OAAiB,CAAC;QACzD,QAAQ,sBAAsB,UAAU;QACxC,OAAO;YACL,MAAM;YACN,SAAS;YACT,WAAW;QACb;IACF,CAAC;AAED,MAAM,eAA4C,CAAC,EACjD,IAAI,EACJ,eAAe,EAAE,EACjB,UAAU,SAAS,EACnB,YAAY,EAAE,EACf;IACC,MAAM,UAAU,MAAM,IAAI,CAAC;IAC3B,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,WAAgC,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,SAAS,GAAG;QAC1B,SAAS,KAAK,CAAC;YACb,QAAQ,QAAQ;YAChB,OAAO;YACP,YAAY,cAAc,cAAc;QAC1C;IACF,GAAG;QAAC;QAAc;QAAM;QAAS;KAAS;IAE1C,MAAM,mBAAmB;QACvB,MAAM,QAAQ,SAAS,GAAG;QAE1B,IAAI,CAAC,SAAS;QAEd,IAAI;QACJ,IAAI,WAAW;QAEf,OAAQ;YACN,KAAK;gBACH,mBAAmB,cAAc,eAAe,GAAG;gBACnD;YACF,KAAK;gBACH,mBAAmB,cAAc,eAAe,GAAG;gBACnD;YACF,KAAK;gBACH,mBAAmB;oBACjB,QAAQ;wBAAE,MAAM;wBAAU,SAAS;wBAAI,WAAW;oBAAI;oBACtD,OAAO;wBAAE,MAAM;wBAAU,SAAS;wBAAI,WAAW;oBAAI;gBACvD;gBACA;YACF,KAAK;gBACH,mBAAmB,cAAc,eAAe,IAAI;gBACpD,WAAW;gBACX;YACF;gBACE,mBAAmB,cAAc,cAAc;QACnD;QAEA,SAAS,KAAK,CAAC;YACb,QAAQ,QAAQ;YAChB,OAAO;YACP,YAAY;QACd;IACF;IAEA,MAAM,iBAAiB;QACrB,MAAM,QAAQ,SAAS,GAAG;QAC1B,SAAS,KAAK,CAAC;YACb,QAAQ,QAAQ;YAChB,OAAO;YACP,YAAY,cAAc,cAAc;QAC1C;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACX,WAAW,CAAC,0GAA0G,EAAE,WAAW;QACnI,OAAO;YAAE,QAAQ;QAAS;QACxB,SAAS;YAAE,QAAQ;QAAE;QACrB,SAAS;QACT,cAAc;QACd,cAAc;kBAEb,QAAQ,GAAG,CAAC,CAAC,QAAQ;YACpB,MAAM,cAAc,AAAC,MAAM,QAAQ,MAAM,GAAI;YAC7C,MAAM,SAAS,KAAK,EAAE,GAAG,QAAQ,MAAM;YACvC,MAAM,IAAI,SAAS;YACnB,MAAM,IAAI,SAAS;YACnB,MAAM,YAAY,CAAC,QAAQ,EAAE,YAAY,iBAAiB,EAAE,EAAE,IAAI,EAAE,EAAE,MAAM,CAAC;YAE7E,qBACE,8OAAC;gBAEC,WAAU;gBACV,OAAO;oBAAE;oBAAW,iBAAiB;gBAAU;0BAE9C;eAJI;;;;;QAOX;;;;;;AAGN;uCAEe", "debugId": null}}, {"offset": {"line": 619, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/main/components/FlippableProfileCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport TiltedCard from './TiltedCard/TiltedCard';\nimport CircularText from './CircularText/CircularText';\n\ninterface FlippableProfileCardProps {\n  imageSrc: string;\n  altText?: string;\n}\n\nexport default function FlippableProfileCard({ \n  imageSrc, \n  altText = \"Abdullah - Full Stack Developer\" \n}: FlippableProfileCardProps) {\n  const [isFlipped, setIsFlipped] = useState(false);\n\n  const handleCardClick = () => {\n    setIsFlipped(!isFlipped);\n  };\n\n  return (\n    <div className=\"relative w-64 h-64 md:w-80 md:h-80 mx-auto [perspective:1000px]\">\n      <motion.div\n        className=\"relative w-full h-full [transform-style:preserve-3d] cursor-pointer\"\n        animate={{ rotateY: isFlipped ? 180 : 0 }}\n        transition={{ duration: 0.8, ease: \"easeInOut\" }}\n        onClick={handleCardClick}\n      >\n        {/* Front Side */}\n        <div className=\"absolute inset-0 w-full h-full [backface-visibility:hidden]\">\n          <TiltedCard\n            imageSrc={imageSrc}\n            altText={altText}\n            captionText=\"you can click it\"\n            containerHeight=\"100%\"\n            containerWidth=\"100%\"\n            imageHeight=\"100%\"\n            imageWidth=\"100%\"\n            scaleOnHover={1.05}\n            rotateAmplitude={12}\n            showMobileWarning={false}\n            showTooltip={true}\n          />\n        </div>\n\n        {/* Back Side */}\n        <div className=\"absolute inset-0 w-full h-full [backface-visibility:hidden] [transform:rotateY(180deg)]\">\n          <div className=\"w-full h-full bg-white border border-gray-200 rounded-3xl flex items-center justify-center shadow-2xl\">\n            <div className=\"relative\">\n              <CircularText\n                text=\"scroll for more info about me • \"\n                spinDuration={20}\n                onHover=\"speedUp\"\n                className=\"text-gray-600 font-medium text-sm\"\n              />\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <div className=\"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center\">\n                  <svg\n                    className=\"w-5 h-5 text-gray-500 animate-bounce\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2}\n                      d=\"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                    />\n                  </svg>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYe,SAAS,qBAAqB,EAC3C,QAAQ,EACR,UAAU,iCAAiC,EACjB;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,kBAAkB;QACtB,aAAa,CAAC;IAChB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS,YAAY,MAAM;YAAE;YACxC,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAY;YAC/C,SAAS;;8BAGT,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,uIAAA,CAAA,UAAU;wBACT,UAAU;wBACV,SAAS;wBACT,aAAY;wBACZ,iBAAgB;wBAChB,gBAAe;wBACf,aAAY;wBACZ,YAAW;wBACX,cAAc;wBACd,iBAAiB;wBACjB,mBAAmB;wBACnB,aAAa;;;;;;;;;;;8BAKjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,2IAAA,CAAA,UAAY;oCACX,MAAK;oCACL,cAAc;oCACd,SAAQ;oCACR,WAAU;;;;;;8CAEZ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxB", "debugId": null}}, {"offset": {"line": 758, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/main/components/ui/animated-tooltip.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useRef } from \"react\";\nimport {\n  motion,\n  useTransform,\n  AnimatePresence,\n  useMotionValue,\n  useSpring,\n} from \"motion/react\";\n\nexport const AnimatedTooltip = ({\n  items,\n}: {\n  items: {\n    id: number;\n    name: string;\n    designation: string;\n    image: string;\n  }[];\n}) => {\n  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);\n  const springConfig = { stiffness: 100, damping: 15 };\n  const x = useMotionValue(0);\n  const animationFrameRef = useRef<number | null>(null);\n\n  const rotate = useSpring(\n    useTransform(x, [-100, 100], [-45, 45]),\n    springConfig,\n  );\n  const translateX = useSpring(\n    useTransform(x, [-100, 100], [-50, 50]),\n    springConfig,\n  );\n\n  const handleMouseMove = (event: any) => {\n    if (animationFrameRef.current) {\n      cancelAnimationFrame(animationFrameRef.current);\n    }\n\n    animationFrameRef.current = requestAnimationFrame(() => {\n      const halfWidth = event.target.offsetWidth / 2;\n      x.set(event.nativeEvent.offsetX - halfWidth);\n    });\n  };\n\n  return (\n    <>\n      {items.map((item, idx) => (\n        <div\n          className=\"group relative -mr-4\"\n          key={item.name}\n          onMouseEnter={() => setHoveredIndex(item.id)}\n          onMouseLeave={() => setHoveredIndex(null)}\n        >\n          <AnimatePresence>\n            {hoveredIndex === item.id && (\n              <motion.div\n                initial={{ opacity: 0, y: 20, scale: 0.6 }}\n                animate={{\n                  opacity: 1,\n                  y: 0,\n                  scale: 1,\n                  transition: {\n                    type: \"spring\",\n                    stiffness: 260,\n                    damping: 10,\n                  },\n                }}\n                exit={{ opacity: 0, y: 20, scale: 0.6 }}\n                style={{\n                  translateX: translateX,\n                  rotate: rotate,\n                  whiteSpace: \"nowrap\",\n                }}\n                className=\"absolute -top-16 left-1/2 z-50 flex -translate-x-1/2 flex-col items-center justify-center rounded-md bg-black px-4 py-2 text-xs shadow-xl\"\n              >\n                <div className=\"absolute inset-x-10 -bottom-px z-30 h-px w-[20%] bg-gradient-to-r from-transparent via-emerald-500 to-transparent\" />\n                <div className=\"absolute -bottom-px left-10 z-30 h-px w-[40%] bg-gradient-to-r from-transparent via-sky-500 to-transparent\" />\n                <div className=\"relative z-30 text-base font-bold text-white\">\n                  {item.name}\n                </div>\n                <div className=\"text-xs text-white\">{item.designation}</div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n          <img\n            onMouseMove={handleMouseMove}\n            height={100}\n            width={100}\n            src={item.image}\n            alt={item.name}\n            className=\"relative !m-0 h-14 w-14 rounded-full border-2 border-white object-cover object-top !p-0 transition duration-500 group-hover:z-30 group-hover:scale-105\"\n          />\n        </div>\n      ))}\n    </>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAWO,MAAM,kBAAkB,CAAC,EAC9B,KAAK,EAQN;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,eAAe;QAAE,WAAW;QAAK,SAAS;IAAG;IACnD,MAAM,IAAI,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IACzB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IAEhD,MAAM,SAAS,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EACrB,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,GAAG;QAAC,CAAC;QAAK;KAAI,EAAE;QAAC,CAAC;QAAI;KAAG,GACtC;IAEF,MAAM,aAAa,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EACzB,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,GAAG;QAAC,CAAC;QAAK;KAAI,EAAE;QAAC,CAAC;QAAI;KAAG,GACtC;IAGF,MAAM,kBAAkB,CAAC;QACvB,IAAI,kBAAkB,OAAO,EAAE;YAC7B,qBAAqB,kBAAkB,OAAO;QAChD;QAEA,kBAAkB,OAAO,GAAG,sBAAsB;YAChD,MAAM,YAAY,MAAM,MAAM,CAAC,WAAW,GAAG;YAC7C,EAAE,GAAG,CAAC,MAAM,WAAW,CAAC,OAAO,GAAG;QACpC;IACF;IAEA,qBACE;kBACG,MAAM,GAAG,CAAC,CAAC,MAAM,oBAChB,8OAAC;gBACC,WAAU;gBAEV,cAAc,IAAM,gBAAgB,KAAK,EAAE;gBAC3C,cAAc,IAAM,gBAAgB;;kCAEpC,8OAAC,yLAAA,CAAA,kBAAe;kCACb,iBAAiB,KAAK,EAAE,kBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;gCAAI,OAAO;4BAAI;4BACzC,SAAS;gCACP,SAAS;gCACT,GAAG;gCACH,OAAO;gCACP,YAAY;oCACV,MAAM;oCACN,WAAW;oCACX,SAAS;gCACX;4BACF;4BACA,MAAM;gCAAE,SAAS;gCAAG,GAAG;gCAAI,OAAO;4BAAI;4BACtC,OAAO;gCACL,YAAY;gCACZ,QAAQ;gCACR,YAAY;4BACd;4BACA,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;8CACZ,KAAK,IAAI;;;;;;8CAEZ,8OAAC;oCAAI,WAAU;8CAAsB,KAAK,WAAW;;;;;;;;;;;;;;;;;kCAI3D,8OAAC;wBACC,aAAa;wBACb,QAAQ;wBACR,OAAO;wBACP,KAAK,KAAK,KAAK;wBACf,KAAK,KAAK,IAAI;wBACd,WAAU;;;;;;;eAzCP,KAAK,IAAI;;;;;;AA+CxB", "debugId": null}}, {"offset": {"line": 903, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/main/components/Dock/Dock.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  motion,\n  MotionValue,\n  useMotionValue,\n  useSpring,\n  useTransform,\n  type SpringOptions,\n  AnimatePresence,\n} from \"framer-motion\";\nimport React, {\n  Children,\n  cloneElement,\n  useEffect,\n  useMemo,\n  useRef,\n  useState,\n} from \"react\";\n\nexport type DockItemData = {\n  icon: React.ReactNode;\n  label: React.ReactNode;\n  onClick: () => void;\n  className?: string;\n};\n\nexport type DockProps = {\n  items: DockItemData[];\n  className?: string;\n  distance?: number;\n  panelHeight?: number;\n  baseItemSize?: number;\n  dockHeight?: number;\n  magnification?: number;\n  spring?: SpringOptions;\n};\n\ntype DockItemProps = {\n  className?: string;\n  children: React.ReactNode;\n  onClick?: () => void;\n  mouseX: MotionValue;\n  spring: SpringOptions;\n  distance: number;\n  baseItemSize: number;\n  magnification: number;\n};\n\nfunction DockItem({\n  children,\n  className = \"\",\n  onClick,\n  mouseX,\n  spring,\n  distance,\n  magnification,\n  baseItemSize,\n}: DockItemProps) {\n  const ref = useRef<HTMLDivElement>(null);\n  const isHovered = useMotionValue(0);\n\n  const mouseDistance = useTransform(mouseX, (val) => {\n    const rect = ref.current?.getBoundingClientRect() ?? {\n      x: 0,\n      width: baseItemSize,\n    };\n    return val - rect.x - baseItemSize / 2;\n  });\n\n  const targetSize = useTransform(\n    mouseDistance,\n    [-distance, 0, distance],\n    [baseItemSize, magnification, baseItemSize]\n  );\n  const size = useSpring(targetSize, spring);\n\n  return (\n    <motion.div\n      ref={ref}\n      style={{\n        width: size,\n        height: size,\n      }}\n      onHoverStart={() => isHovered.set(1)}\n      onHoverEnd={() => isHovered.set(0)}\n      onFocus={() => isHovered.set(1)}\n      onBlur={() => isHovered.set(0)}\n      onClick={onClick}\n      className={`relative inline-flex items-center justify-center rounded-2xl bg-white/90 backdrop-blur-xl border border-white/20 shadow-xl hover:shadow-2xl hover:bg-white/95 transition-all duration-200 cursor-pointer active:scale-95 ${className}`}\n      tabIndex={0}\n      role=\"button\"\n      aria-haspopup=\"true\"\n      whileHover={{ y: -2 }}\n      whileTap={{ scale: 0.95 }}\n    >\n      {Children.map(children, (child) =>\n        cloneElement(child as React.ReactElement, { isHovered })\n      )}\n    </motion.div>\n  );\n}\n\ntype DockLabelProps = {\n  className?: string;\n  children: React.ReactNode;\n};\n\nfunction DockLabel({ children, className = \"\", ...rest }: DockLabelProps) {\n  const { isHovered } = rest as { isHovered: MotionValue<number> };\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const unsubscribe = isHovered.on(\"change\", (latest) => {\n      setIsVisible(latest === 1);\n    });\n    return () => unsubscribe();\n  }, [isHovered]);\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          initial={{ opacity: 0, y: 10, scale: 0.8 }}\n          animate={{ opacity: 1, y: -15, scale: 1 }}\n          exit={{ opacity: 0, y: 10, scale: 0.8 }}\n          transition={{\n            duration: 0.2,\n            ease: [0.16, 1, 0.3, 1],\n            scale: { duration: 0.15 }\n          }}\n          className={`${className} absolute -top-12 left-1/2 w-fit whitespace-nowrap rounded-lg border border-gray-200/50 bg-gray-900/95 backdrop-blur-xl px-3 py-2 text-sm font-medium text-white shadow-2xl`}\n          role=\"tooltip\"\n          style={{\n            x: \"-50%\",\n            filter: \"drop-shadow(0 10px 25px rgba(0, 0, 0, 0.3))\"\n          }}\n        >\n          {children}\n          {/* Tooltip arrow */}\n          <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-gray-900/95\"></div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n\ntype DockIconProps = {\n  className?: string;\n  children: React.ReactNode;\n};\n\nfunction DockIcon({ children, className = \"\" }: DockIconProps) {\n  return (\n    <div className={`flex items-center justify-center ${className}`}>\n      {children}\n    </div>\n  );\n}\n\nexport default function Dock({\n  items,\n  className = \"\",\n  spring = { mass: 0.05, stiffness: 200, damping: 15 },\n  magnification = 90,\n  distance = 150,\n  panelHeight = 64,\n  dockHeight = 256,\n  baseItemSize = 50,\n}: DockProps) {\n  const mouseX = useMotionValue(Infinity);\n  const isHovered = useMotionValue(0);\n\n  const maxHeight = useMemo(\n    () => Math.max(dockHeight, magnification + magnification / 2 + 4),\n    [magnification]\n  );\n  const heightRow = useTransform(isHovered, [0, 1], [panelHeight, maxHeight]);\n  const height = useSpring(heightRow, spring);\n\n  return (\n    <motion.div\n      style={{ height, scrollbarWidth: \"none\" }}\n      className=\"mx-2 flex max-w-full items-center\"\n    >\n      <motion.div\n        onMouseMove={({ pageX }) => {\n          isHovered.set(1);\n          mouseX.set(pageX);\n        }}\n        onMouseLeave={() => {\n          isHovered.set(0);\n          mouseX.set(Infinity);\n        }}\n        className={`${className} absolute bottom-6 left-1/2 transform -translate-x-1/2 flex items-end w-fit gap-2 rounded-3xl bg-white/80 backdrop-blur-2xl border border-white/30 pb-4 px-6 shadow-2xl`}\n        style={{\n          height: panelHeight,\n          filter: \"drop-shadow(0 25px 50px rgba(0, 0, 0, 0.15))\"\n        }}\n        role=\"toolbar\"\n        aria-label=\"Application dock\"\n      >\n        {items.map((item, index) => (\n          <DockItem\n            key={index}\n            onClick={item.onClick}\n            className={item.className}\n            mouseX={mouseX}\n            spring={spring}\n            distance={distance}\n            magnification={magnification}\n            baseItemSize={baseItemSize}\n          >\n            <DockIcon>{item.icon}</DockIcon>\n            <DockLabel>{item.label}</DockLabel>\n          </DockItem>\n        ))}\n      </motion.div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AASA;AAXA;;;;AAiDA,SAAS,SAAS,EAChB,QAAQ,EACR,YAAY,EAAE,EACd,OAAO,EACP,MAAM,EACN,MAAM,EACN,QAAQ,EACR,aAAa,EACb,YAAY,EACE;IACd,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACnC,MAAM,YAAY,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IAEjC,MAAM,gBAAgB,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,CAAC;QAC1C,MAAM,OAAO,IAAI,OAAO,EAAE,2BAA2B;YACnD,GAAG;YACH,OAAO;QACT;QACA,OAAO,MAAM,KAAK,CAAC,GAAG,eAAe;IACvC;IAEA,MAAM,aAAa,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAC5B,eACA;QAAC,CAAC;QAAU;QAAG;KAAS,EACxB;QAAC;QAAc;QAAe;KAAa;IAE7C,MAAM,OAAO,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,YAAY;IAEnC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,OAAO;YACL,OAAO;YACP,QAAQ;QACV;QACA,cAAc,IAAM,UAAU,GAAG,CAAC;QAClC,YAAY,IAAM,UAAU,GAAG,CAAC;QAChC,SAAS,IAAM,UAAU,GAAG,CAAC;QAC7B,QAAQ,IAAM,UAAU,GAAG,CAAC;QAC5B,SAAS;QACT,WAAW,CAAC,yNAAyN,EAAE,WAAW;QAClP,UAAU;QACV,MAAK;QACL,iBAAc;QACd,YAAY;YAAE,GAAG,CAAC;QAAE;QACpB,UAAU;YAAE,OAAO;QAAK;kBAEvB,qMAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,sBACvB,CAAA,GAAA,qMAAA,CAAA,eAAY,AAAD,EAAE,OAA6B;gBAAE;YAAU;;;;;;AAI9D;AAOA,SAAS,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,GAAG,MAAsB;IACtE,MAAM,EAAE,SAAS,EAAE,GAAG;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,UAAU,EAAE,CAAC,UAAU,CAAC;YAC1C,aAAa,WAAW;QAC1B;QACA,OAAO,IAAM;IACf,GAAG;QAAC;KAAU;IAEd,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;gBAAI,OAAO;YAAI;YACzC,SAAS;gBAAE,SAAS;gBAAG,GAAG,CAAC;gBAAI,OAAO;YAAE;YACxC,MAAM;gBAAE,SAAS;gBAAG,GAAG;gBAAI,OAAO;YAAI;YACtC,YAAY;gBACV,UAAU;gBACV,MAAM;oBAAC;oBAAM;oBAAG;oBAAK;iBAAE;gBACvB,OAAO;oBAAE,UAAU;gBAAK;YAC1B;YACA,WAAW,GAAG,UAAU,2KAA2K,CAAC;YACpM,MAAK;YACL,OAAO;gBACL,GAAG;gBACH,QAAQ;YACV;;gBAEC;8BAED,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAKzB;AAOA,SAAS,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAiB;IAC3D,qBACE,8OAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;kBAC5D;;;;;;AAGP;AAEe,SAAS,KAAK,EAC3B,KAAK,EACL,YAAY,EAAE,EACd,SAAS;IAAE,MAAM;IAAM,WAAW;IAAK,SAAS;AAAG,CAAC,EACpD,gBAAgB,EAAE,EAClB,WAAW,GAAG,EACd,cAAc,EAAE,EAChB,aAAa,GAAG,EAChB,eAAe,EAAE,EACP;IACV,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IAC9B,MAAM,YAAY,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IAEjC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACtB,IAAM,KAAK,GAAG,CAAC,YAAY,gBAAgB,gBAAgB,IAAI,IAC/D;QAAC;KAAc;IAEjB,MAAM,YAAY,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,WAAW;QAAC;QAAG;KAAE,EAAE;QAAC;QAAa;KAAU;IAC1E,MAAM,SAAS,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,WAAW;IAEpC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,OAAO;YAAE;YAAQ,gBAAgB;QAAO;QACxC,WAAU;kBAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,aAAa,CAAC,EAAE,KAAK,EAAE;gBACrB,UAAU,GAAG,CAAC;gBACd,OAAO,GAAG,CAAC;YACb;YACA,cAAc;gBACZ,UAAU,GAAG,CAAC;gBACd,OAAO,GAAG,CAAC;YACb;YACA,WAAW,GAAG,UAAU,uKAAuK,CAAC;YAChM,OAAO;gBACL,QAAQ;gBACR,QAAQ;YACV;YACA,MAAK;YACL,cAAW;sBAEV,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;oBAEC,SAAS,KAAK,OAAO;oBACrB,WAAW,KAAK,SAAS;oBACzB,QAAQ;oBACR,QAAQ;oBACR,UAAU;oBACV,eAAe;oBACf,cAAc;;sCAEd,8OAAC;sCAAU,KAAK,IAAI;;;;;;sCACpB,8OAAC;sCAAW,KAAK,KAAK;;;;;;;mBAVjB;;;;;;;;;;;;;;;AAgBjB", "debugId": null}}, {"offset": {"line": 1128, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/main/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from \"react\";\nimport SplitText from \"../components/SplitText/SplitText\";\nimport ScrollVelocity from \"../components/ScrollVelocity/ScrollVelocity\";\nimport BlurText from \"../components/BlurText/BlurText\";\nimport FlippableProfileCard from \"../components/FlippableProfileCard\";\nimport { AnimatedTooltip } from \"../components/ui/animated-tooltip\";\nimport Dock from \"../components/Dock/Dock\";\n\nexport default function Home() {\n  const [isLoaded, setIsLoaded] = useState(false);\n\n  useEffect(() => {\n    setIsLoaded(true);\n  }, []);\n\n  // Happy clients data for animated tooltip - using professional photos\n  const happyClients = [\n    {\n      id: 1,\n      name: \"<PERSON>\",\n      designation: \"Software Engineer\",\n      image: \"https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=3387&q=80\",\n    },\n    {\n      id: 2,\n      name: \"<PERSON>\",\n      designation: \"Product Manager\",\n      image: \"https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8YXZhdGFyfGVufDB8fDB8fHww&auto=format&fit=crop&w=800&q=60\",\n    },\n    {\n      id: 3,\n      name: \"Jane Smith\",\n      designation: \"Data Scientist\",\n      image: \"https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8YXZhdGFyfGVufDB8fDB8fHww&auto=format&fit=crop&w=800&q=60\",\n    },\n    {\n      id: 4,\n      name: \"Emily Davis\",\n      designation: \"UX Designer\",\n      image: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGF2YXRhcnxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=800&q=60\",\n    },\n    {\n      id: 5,\n      name: \"Tyler Durden\",\n      designation: \"Soap Developer\",\n      image: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=3540&q=80\",\n    },\n    {\n      id: 6,\n      name: \"Dora\",\n      designation: \"The Explorer\",\n      image: \"https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=3534&q=80\",\n    },\n  ];\n\n  // Dock navigation items\n  const dockItems = [\n    {\n      icon: (\n        <svg className=\"w-7 h-7 text-gray-800\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n        </svg>\n      ),\n      label: \"Home\",\n      onClick: () => console.log(\"Home clicked\"),\n    },\n    {\n      icon: (\n        <svg className=\"w-7 h-7 text-gray-800\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n        </svg>\n      ),\n      label: \"About\",\n      onClick: () => console.log(\"About clicked\"),\n    },\n    {\n      icon: (\n        <svg className=\"w-7 h-7 text-gray-800\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n        </svg>\n      ),\n      label: \"Projects\",\n      onClick: () => console.log(\"Projects clicked\"),\n    },\n    {\n      icon: (\n        <svg className=\"w-7 h-7 text-gray-800\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n        </svg>\n      ),\n      label: \"Contact\",\n      onClick: () => console.log(\"Contact clicked\"),\n    },\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 relative overflow-hidden\">\n      {/* Full Background Scrolling Text */}\n      <div className=\"fixed inset-0 flex items-center justify-center pointer-events-none z-10\">\n        <ScrollVelocity\n          texts={[\"make experiences you will never forget\"]}\n          velocity={30}\n          className=\"text-gray-200\"\n          parallaxClassName=\"w-full\"\n          scrollerClassName=\"text-4xl md:text-7xl lg:text-8xl font-black whitespace-nowrap tracking-widest opacity-20\"\n        />\n      </div>\n\n      {/* Main hero content */}\n      <div className=\"container mx-auto px-6 pt-16 pb-32 relative z-30\">\n        <div className=\"text-center max-w-5xl mx-auto\">\n\n          {/* Profile Card */}\n          <div className=\"relative mb-10\">\n            <div className={`relative z-20 transition-all duration-1200 delay-300 ${isLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}>\n              <FlippableProfileCard\n                imageSrc=\"/profile-placeholder.svg\"\n                altText=\"Abdullah - Full Stack Developer\"\n              />\n            </div>\n          </div>\n\n          {/* Name with Split Text Animation - UNDER the profile card */}\n          <div className={`mb-8 transition-all duration-1000 delay-500 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}>\n            <SplitText\n              text=\"Abdullah\"\n              className=\"text-6xl md:text-8xl lg:text-9xl font-black text-black tracking-tight\"\n              delay={120}\n              duration={0.9}\n              splitType=\"chars\"\n              from={{ opacity: 0, y: 60, rotateX: -90, scale: 0.8 }}\n              to={{ opacity: 1, y: 0, rotateX: 0, scale: 1 }}\n            />\n          </div>\n\n          {/* Subtitle with Blur Text Animation - UNDER the name */}\n          <div className={`mb-16 transition-all duration-1000 delay-700 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}>\n            <div className=\"text-xl md:text-2xl lg:text-3xl text-gray-700 flex justify-center\">\n              <BlurText\n                text=\"Full Stack Developer & UI/UX Designer\"\n                className=\"font-medium tracking-wide\"\n                delay={80}\n                animateBy=\"words\"\n                direction=\"bottom\"\n              />\n            </div>\n          </div>\n\n          {/* Clean Client Avatars */}\n          <div className={`flex justify-center mb-16 transition-all duration-1000 delay-900 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>\n            <div className=\"flex items-center\">\n              <AnimatedTooltip items={happyClients} />\n            </div>\n          </div>\n\n          {/* CTA Button */}\n          <div className={`transition-all duration-1000 delay-1100 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>\n            <button className=\"bg-gradient-to-r from-black to-gray-800 text-white px-12 py-5 rounded-full font-bold text-xl hover:from-gray-800 hover:to-black transition-all duration-300 hover:scale-105 shadow-2xl hover:shadow-3xl transform hover:-translate-y-1\">\n              Let's Work Together!\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Bottom Dock Navigation */}\n      <div className=\"fixed bottom-0 left-0 right-0 z-50\">\n        <Dock\n          items={dockItems}\n          magnification={80}\n          distance={140}\n          baseItemSize={56}\n          panelHeight={72}\n          spring={{ mass: 0.1, stiffness: 300, damping: 20 }}\n        />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,sEAAsE;IACtE,MAAM,eAAe;QACnB;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;<PERSON><PERSON><PERSON>,MAAM;YAC<PERSON>,aAAa;YAC<PERSON>,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;QACT;KACD;IAED,wBAAwB;IACxB,MAAM,YAAY;QAChB;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC7B;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC7B;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC7B;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC7B;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,+IAAA,CAAA,UAAc;oBACb,OAAO;wBAAC;qBAAyC;oBACjD,UAAU;oBACV,WAAU;oBACV,mBAAkB;oBAClB,mBAAkB;;;;;;;;;;;0BAKtB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAGb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAW,CAAC,qDAAqD,EAAE,WAAW,0BAA0B,sBAAsB;0CACjI,cAAA,8OAAC,mIAAA,CAAA,UAAoB;oCACnB,UAAS;oCACT,SAAQ;;;;;;;;;;;;;;;;sCAMd,8OAAC;4BAAI,WAAW,CAAC,4CAA4C,EAAE,WAAW,gBAAgB,aAAa;sCACrG,cAAA,8OAAC,qIAAA,CAAA,UAAS;gCACR,MAAK;gCACL,WAAU;gCACV,OAAO;gCACP,UAAU;gCACV,WAAU;gCACV,MAAM;oCAAE,SAAS;oCAAG,GAAG;oCAAI,SAAS,CAAC;oCAAI,OAAO;gCAAI;gCACpD,IAAI;oCAAE,SAAS;oCAAG,GAAG;oCAAG,SAAS;oCAAG,OAAO;gCAAE;;;;;;;;;;;sCAKjD,8OAAC;4BAAI,WAAW,CAAC,6CAA6C,EAAE,WAAW,gBAAgB,aAAa;sCACtG,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,mIAAA,CAAA,UAAQ;oCACP,MAAK;oCACL,WAAU;oCACV,OAAO;oCACP,WAAU;oCACV,WAAU;;;;;;;;;;;;;;;;sCAMhB,8OAAC;4BAAI,WAAW,CAAC,iEAAiE,EAAE,WAAW,8BAA8B,2BAA2B;sCACtJ,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,wIAAA,CAAA,kBAAe;oCAAC,OAAO;;;;;;;;;;;;;;;;sCAK5B,8OAAC;4BAAI,WAAW,CAAC,wCAAwC,EAAE,WAAW,8BAA8B,2BAA2B;sCAC7H,cAAA,8OAAC;gCAAO,WAAU;0CAAyO;;;;;;;;;;;;;;;;;;;;;;0BAQjQ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,2HAAA,CAAA,UAAI;oBACH,OAAO;oBACP,eAAe;oBACf,UAAU;oBACV,cAAc;oBACd,aAAa;oBACb,QAAQ;wBAAE,MAAM;wBAAK,WAAW;wBAAK,SAAS;oBAAG;;;;;;;;;;;;;;;;;AAK3D", "debugId": null}}]}